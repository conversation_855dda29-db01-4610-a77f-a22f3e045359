// 全局类型声明
declare global {
  interface Window {
    __MICRO_APP_NAME__?: string;
    __MICRO_APP_ENVIRONMENT__?: string;
  }
}

// React 相关类型声明
declare module 'react/jsx-runtime' {
  export default any;
  export const jsx: any;
  export const jsxs: any;
  export const Fragment: any;
}

declare module 'react/jsx-dev-runtime' {
  export default any;
  export const jsx: any;
  export const jsxs: any;
  export const Fragment: any;
}

// 修复一些常见的类型问题
declare module '*.scss' {
  const content: { [className: string]: string };
  export default content;
}

declare module '*.css' {
  const content: { [className: string]: string };
  export default content;
}

// 导出空对象以使此文件成为模块
export {};
