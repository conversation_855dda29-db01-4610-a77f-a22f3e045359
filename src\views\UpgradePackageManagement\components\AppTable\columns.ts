import { ProductType, ProductTypeName } from '@/utils/constant';
import { FormConfig } from '@jd/x-coreui/es/components';

export const AppTableData: any[] = [
  {
    title: '应用ID',
    dataIndex: 'appId',
    align: 'center',
    width: 200,
  },
  {
    title: '所属产品',
    dataIndex: 'productTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '应用名称',
    dataIndex: 'appAlias',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '英文名',
    dataIndex: 'appName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '负责人',
    dataIndex: 'developer',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '修改时间',
    dataIndex: 'modifyTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '修改人',
    dataIndex: 'modifyUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 300,
    fixed: 'right',
  },
];

export const AppFormData: FormConfig = {
  fields: [
    {
      fieldName: 'productType',
      label: '所属产品',
      placeholder: '请输入关键字',
      type: 'radioGroup',
      specialFetch: 'commonDown',
      validatorRules: [
        {
          // 校验规则
          required: true,
          message: '请选择所属产品',
        },
      ],
      options: [
        {
          label: ProductTypeName.VEHICLE,
          value: ProductType.VEHICLE,
        },
        {
          label: ProductTypeName.ROBOT,
          value: ProductType.ROBOT,
        },
      ],
    },
    {
      fieldName: 'appAlias',
      label: '应用名称',
      placeholder: '请输入应用名称',
      type: 'input',
      maxLength: 20,
      validatorRules: [
        {
          // 校验规则
          required: true,
          message: '请填写应用名称',
        },
      ],
    },
    {
      fieldName: 'appName',
      label: '英文名',
      placeholder: '请输入应用英文名',
      type: 'input',
      maxLength: 20,
      validatorRules: [
        {
          // 校验规则
          required: true,
          message: '请填写应英文名',
        },
      ],
    },
    {
      fieldName: 'developer',
      label: '负责人',
      placeholder: '请输入负责人',
      type: 'input',
      maxLength: 20,
      validatorRules: [
        {
          // 校验规则
          required: true,
          message: '请填写负责人姓名',
        },
      ],
    },
  ],
};

export const VersionTableData: any[] = [
  {
    title: '版本编号',
    dataIndex: 'versionNumber',
    width: 200,
  },
  {
    title: '应用',
    dataIndex: 'appName',
    ellipsis: true,
  },
  {
    title: '所属产品',
    dataIndex: 'productTypeName',
    ellipsis: true,
  },
  {
    title: '所属设备',
    dataIndex: 'applyVehicleBusinessTypeName',
    ellipsis: true,
  },
  {
    title: '版本号',
    dataIndex: 'version',
    ellipsis: true,
  },
  {
    title: '更新内容',
    dataIndex: 'updateInfo',
    ellipsis: true,
  },
  {
    title: '缺陷',
    dataIndex: 'defect',
    ellipsis: true,
  },
  {
    title: '描述',
    dataIndex: 'description',
    fixed: 'right',
    ellipsis: true,
  },
  {
    title: '扩展信息',
    dataIndex: 'extData',
    align: 'center',
    fixed: 'right',
    ellipsis: true,
  },
  {
    title: '更新时间',
    dataIndex: 'modifyTime',
    fixed: 'right',
  },
  {
    title: '最后操作人',
    dataIndex: 'modifyUser',
    fixed: 'right',
  },
  {
    title: '操作',
    dataIndex: 'operate',
    fixed: 'right',
    width: 200,
  },
];

export const AppPackageForm: FormConfig = {
  fields: [
    {
      fieldName: 'appName',
      label: '应用',
      type: 'input',
      disabled: true,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      labelInValue: false,
    },
    {
      fieldName: 'productTypeName',
      label: '所属产品',
      type: 'input',
      disabled: true,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      labelInValue: false,
    },
    {
      fieldName: 'applyVehicleBusinessType',
      label: '所属设备',
      type: 'radioGroup',
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      labelInValue: false,
    },
    {
      fieldName: 'version',
      label: '版本号',
      type: 'input',
      placeholder: '请填写版本号',
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      labelInValue: false,
      validatorRules: [
        {
          required: true,
          validator: (rule: any, value: any) => {
            if (!value) {
              return Promise.reject(new Error('请填写版本号'));
            }
            const regex = /^[a-zA-Z0-9._-]+$/;
            const result = regex.test(value);
            if (result) {
              return Promise.resolve();
            } else {
              return Promise.reject(new Error('请输入英文、数字、.、-、_'));
            }
          },
        },
      ],
    },
    {
      fieldName: 'packageType',
      label: '应用包格式',
      type: 'select',
      placeholder: '请选择应用包格式',
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      labelInValue: false,
      validatorRules: [
        {
          required: true,
          message: '请选择应用包格式',
        },
      ],
      options: [
        {
          label: 'apk',
          value: 'apk',
        },
        {
          label: 'zip',
          value: 'zip',
        },
      ],
    },
    {
      label: '上传应用包',
      fieldName: 'file',
      type: 'upload',
      fileListType: 'file',
      labelInValue: false,
      accept: '',
      LOPDN: process.env.JDX_APP_REQUEST_HEADER!,
      bucketName: 'rover-operation',
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      max: 5,
      maxFileSize: 200,
      unit: 'MB',
      getPreSignatureUrl:
        location.protocol +
        '//' +
        process.env.JDX_APP_CLOUD_FETCH_DOMAIN +
        '/k2/oss/upload',
      validatorRules: [
        {
          required: false,
          message: '请上传应用包',
        },
      ],
    },
    {
      fieldName: 'updateInfo',
      label: '更新内容',
      type: 'textarea',
      labelInValue: false,
      maxLength: 1000,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
    },
    {
      fieldName: 'defect',
      label: '缺陷',
      type: 'textarea',
      maxLength: 1000,
      labelInValue: false,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
    },
    {
      fieldName: 'description',
      label: '备注信息',
      type: 'textarea',
      maxLength: 500,
      labelInValue: false,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
    },
    {
      fieldName: 'extData',
      label: '扩展信息',
      type: 'textarea',
      maxLength: 1000,
      labelInValue: false,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
    },
  ],
  linkRules: {
    packageType: [
      {
        linkFieldName: 'file',
        rule: 'refresh',
        refreshFunc: (val: any, needChangeField: any) => {
          if (val) {
            needChangeField.accept = val;
          }
        },
      },
    ],
  },
};
