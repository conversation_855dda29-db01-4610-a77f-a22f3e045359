import React, { useEffect } from 'react';
import { Modal } from 'antd';
import { CustomButton, ButtonType } from '../CustomButton';
import ReactDiffViewer, { DiffMethod } from 'react-diff-viewer';
import './index.scss';

interface Props {
  compareModalShow: boolean; // 是否显示modal
  content: CompareContent; // 要对比的内容
  footer: React.ReactNode;
  title?: React.ReactNode;
  onCancel: AnyFunc; // 关闭弹窗
}
interface CompareContent {
  leftTitle: string;
  leftValue: string;
  rightTitle: string;
  rightValue: string;
}
const CompareModal = (props: Props) => {
  const { onCancel, compareModalShow, content, footer, title } = props;
  return (
    <Modal
      className="compare-modal"
      width={'1400px'}
      title={title ?? '操作内容对比'}
      onCancel={() => {
        onCancel();
      }}
      visible={compareModalShow}
      footer={<div>{footer}</div>}
    >
      <ReactDiffViewer
        leftTitle={content?.leftTitle}
        rightTitle={content?.rightTitle}
        oldValue={content?.leftValue}
        newValue={content?.rightValue}
        compareMethod={DiffMethod.WORDS}
        splitView={true}
      />
    </Modal>
  );
};

export default React.memo(CompareModal);
