/* eslint-disable no-unused-vars */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../store';
export declare type NavigatParams = {
  routeName: any, // 路由名称
  searchValues: any // 搜索条件数据
}
const initialState: NavigatParams = {
  routeName: null,
  searchValues: null,
};
const searchformSlice = createSlice({
  name: 'searchform',
  initialState,
  reducers: {
    saveSearchValues(state, event: PayloadAction<NavigatParams>) {
      const { routeName, searchValues } = event.payload
      state.routeName = routeName
      state.searchValues = searchValues
    },
    removeSearchValues(state, event) {
      state.routeName = null
      state.searchValues = null
    }
  },
});

export const searchformReducer = searchformSlice.reducer;
export const {
  saveSearchValues,
  removeSearchValues,
} = searchformSlice.actions;
export const searchformSelector = (state: RootState) => state.searchform;

