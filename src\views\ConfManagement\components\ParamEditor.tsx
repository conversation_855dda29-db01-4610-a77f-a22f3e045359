import React, { useState } from 'react';
import { Form, Input, Select, Button, Row, Col, Divider, Space, Tooltip, InputNumber, Switch } from 'antd';
import { PlusOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import './ParamEditor.scss';

// 数据类型枚举
export enum DataType {
  INT = 'int',
  LONG = 'long',
  DOUBLE = 'double',
  ENUM = 'enum',
  INT_ENUM = 'int_enum',
  BOOL = 'bool',
  TEXT = 'text',
  DATE = 'date',
  ARRAY = 'array',
}

// 数据类型选项
export const dataTypeOptions = [
  { label: 'int32 (整型)', value: DataType.INT },
  { label: 'long (长整型)', value: DataType.LONG },
  { label: 'double (双精度浮点型)', value: DataType.DOUBLE },
  { label: 'enum (枚举型)', value: DataType.ENUM },
  { label: 'int_enum (整型枚举)', value: DataType.INT_ENUM },
  { label: 'bool (布尔型)', value: DataType.BOOL },
  { label: 'text (字符串)', value: DataType.TEXT },
  { label: 'date (日期型)', value: DataType.DATE },
  { label: 'array (数组)', value: DataType.ARRAY },
];

// 数组元素类型选项
export const arrayElementTypeOptions = [
  { label: 'int32', value: 'int32' },
  { label: 'float', value: 'float' },
  { label: 'double', value: 'double' },
  { label: 'text', value: 'text' },
  { label: 'struct', value: 'struct' },
];

// 枚举项接口
export interface EnumItem {
  value: string;
  description: string;
}

// 布尔值映射接口
export interface BoolMapping {
  falseLabel: string;
  trueLabel: string;
}

// 参数项接口
export interface ParamItem {
  id: string;
  paramName: string;
  paramKey: string;
  dataType: DataType;
  description?: string;
  defaultValue?: any;
  enumValues?: EnumItem[]; // 枚举项列表
  boolMapping?: BoolMapping; // 布尔值映射
  required?: boolean;
  min?: number;
  max?: number;
  step?: number; // 步长
  unit?: string; // 单位
  precision?: number;
  format?: string;
  maxLength?: number; // 文本最大长度
  arrayElementType?: string; // 数组元素类型
  arraySize?: number; // 数组元素个数
  items?: ParamItem; // 用于数组类型
  properties?: ParamItem[]; // 用于对象类型
}

interface ParamEditorProps {
  params: ParamItem[];
  onChange: (params: ParamItem[]) => void;
}

const ParamEditor: React.FC<ParamEditorProps> = ({ params, onChange }) => {
  // 添加参数
  const addParam = () => {
    const newParam: ParamItem = {
      id: Date.now().toString(),
      paramName: '',
      paramKey: '',
      dataType: DataType.INT,
      required: true,
    };
    onChange([...params, newParam]);
  };

  // 删除参数
  const removeParam = (id: string) => {
    onChange(params.filter(param => param.id !== id));
  };

  // 查看参数详情
  const viewParam = (param: ParamItem) => {
    console.log('参数详情:', param);
  };

  // 更新参数
  const updateParam = (id: string, field: string, value: any) => {
    onChange(
      params.map(param => {
        if (param.id === id) {
          // 如果更改了数据类型，重置特定类型的字段
          if (field === 'dataType') {
            const updatedParam = { ...param, [field]: value };
            // 根据新的数据类型清除不相关的字段
            switch (value) {
              case DataType.INT:
              case DataType.LONG:
              case DataType.DOUBLE:
                // 数值类型保留 min, max, step, unit
                delete updatedParam.enumValues;
                delete updatedParam.boolMapping;
                delete updatedParam.format;
                delete updatedParam.maxLength;
                delete updatedParam.arrayElementType;
                delete updatedParam.arraySize;
                delete updatedParam.items;
                delete updatedParam.properties;
                break;
              case DataType.ENUM:
              case DataType.INT_ENUM:
                // 枚举类型保留 enumValues
                delete updatedParam.min;
                delete updatedParam.max;
                delete updatedParam.step;
                delete updatedParam.unit;
                delete updatedParam.precision;
                delete updatedParam.boolMapping;
                delete updatedParam.format;
                delete updatedParam.maxLength;
                delete updatedParam.arrayElementType;
                delete updatedParam.arraySize;
                delete updatedParam.items;
                delete updatedParam.properties;
                if (!updatedParam.enumValues) {
                  updatedParam.enumValues = [];
                }
                break;
              case DataType.BOOL:
                // 布尔类型保留 boolMapping
                delete updatedParam.min;
                delete updatedParam.max;
                delete updatedParam.step;
                delete updatedParam.unit;
                delete updatedParam.precision;
                delete updatedParam.enumValues;
                delete updatedParam.format;
                delete updatedParam.maxLength;
                delete updatedParam.arrayElementType;
                delete updatedParam.arraySize;
                delete updatedParam.items;
                delete updatedParam.properties;
                if (!updatedParam.boolMapping) {
                  updatedParam.boolMapping = { falseLabel: '关', trueLabel: '开' };
                }
                break;
              case DataType.TEXT:
                // 文本类型保留 maxLength
                delete updatedParam.min;
                delete updatedParam.max;
                delete updatedParam.step;
                delete updatedParam.unit;
                delete updatedParam.precision;
                delete updatedParam.enumValues;
                delete updatedParam.boolMapping;
                delete updatedParam.format;
                delete updatedParam.arrayElementType;
                delete updatedParam.arraySize;
                delete updatedParam.items;
                delete updatedParam.properties;
                break;
              case DataType.DATE:
                // 日期类型保留 format
                delete updatedParam.min;
                delete updatedParam.max;
                delete updatedParam.step;
                delete updatedParam.unit;
                delete updatedParam.precision;
                delete updatedParam.enumValues;
                delete updatedParam.boolMapping;
                delete updatedParam.maxLength;
                delete updatedParam.arrayElementType;
                delete updatedParam.arraySize;
                delete updatedParam.items;
                delete updatedParam.properties;
                break;
              case DataType.ARRAY:
                // 数组类型保留 arrayElementType, arraySize
                delete updatedParam.min;
                delete updatedParam.max;
                delete updatedParam.step;
                delete updatedParam.unit;
                delete updatedParam.precision;
                delete updatedParam.enumValues;
                delete updatedParam.boolMapping;
                delete updatedParam.format;
                delete updatedParam.maxLength;
                delete updatedParam.properties;
                if (!updatedParam.arrayElementType) {
                  updatedParam.arrayElementType = 'int32';
                }
                break;
            }
            return updatedParam;
          }
          return { ...param, [field]: value };
        }
        return param;
      })
    );
  };

  // 添加枚举项
  const addEnumItem = (paramId: string) => {
    const param = params.find(p => p.id === paramId);
    if (param) {
      const newEnumValues = [...(param.enumValues || []), { value: '', description: '' }];
      updateParam(paramId, 'enumValues', newEnumValues);
    }
  };

  // 删除枚举项
  const removeEnumItem = (paramId: string, index: number) => {
    const param = params.find(p => p.id === paramId);
    if (param && param.enumValues) {
      const newEnumValues = param.enumValues.filter((_, i) => i !== index);
      updateParam(paramId, 'enumValues', newEnumValues);
    }
  };

  // 更新枚举项
  const updateEnumItem = (paramId: string, index: number, field: 'value' | 'description', value: string) => {
    const param = params.find(p => p.id === paramId);
    if (param && param.enumValues) {
      const newEnumValues = [...param.enumValues];
      newEnumValues[index] = { ...newEnumValues[index], [field]: value };
      updateParam(paramId, 'enumValues', newEnumValues);
    }
  };

  // 渲染不同数据类型的参数输入控件
  const renderParamFields = (param: ParamItem) => {
    switch (param.dataType) {
      case DataType.INT:
      case DataType.LONG:
      case DataType.DOUBLE:
        return (
          <div className="param-type-fields">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="取值范围">
                  <Input.Group compact>
                    <InputNumber
                      placeholder="最小值"
                      value={param.min}
                      onChange={value => updateParam(param.id, 'min', value)}
                      style={{ width: '45%' }}
                    />
                    <Input
                      style={{ width: '10%', textAlign: 'center', pointerEvents: 'none' }}
                      placeholder="~"
                      disabled
                    />
                    <InputNumber
                      placeholder="最大值"
                      value={param.max}
                      onChange={value => updateParam(param.id, 'max', value)}
                      style={{ width: '45%' }}
                    />
                  </Input.Group>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="步长">
                  <Input
                    placeholder="请输入步长"
                    value={param.step}
                    onChange={e => updateParam(param.id, 'step', e.target.value)}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="单位">
                  <Select
                    placeholder="请选择单位"
                    value={param.unit}
                    onChange={value => updateParam(param.id, 'unit', value)}
                    options={[
                      { label: '无单位', value: '' },
                      { label: '米', value: 'm' },
                      { label: '千米', value: 'km' },
                      { label: '厘米', value: 'cm' },
                      { label: '毫米', value: 'mm' },
                      { label: '秒', value: 's' },
                      { label: '分钟', value: 'min' },
                      { label: '小时', value: 'h' },
                      { label: '天', value: 'd' },
                      { label: '摄氏度', value: '°C' },
                      { label: '华氏度', value: '°F' },
                      { label: '百分比', value: '%' },
                    ]}
                  />
                </Form.Item>
              </Col>
            </Row>
          </div>
        );
      case DataType.ENUM:
      case DataType.INT_ENUM:
        return (
          <div className="param-type-fields">
            <div className="enum-section">
              <div className="enum-header">
                <Form.Item label="枚举项" style={{ marginBottom: 16 }}>
                  <Button
                    type="link"
                    onClick={() => addEnumItem(param.id)}
                    style={{ padding: 0 }}
                  >
                    + 添加枚举项
                  </Button>
                </Form.Item>
              </div>
              {(param.enumValues || []).map((enumItem, index) => (
                <div key={index} className="enum-item" style={{ marginBottom: 16 }}>
                  <Row gutter={16} align="middle">
                    <Col span={10}>
                      <Input
                        placeholder="编号如'0'"
                        value={enumItem.value}
                        onChange={e => updateEnumItem(param.id, index, 'value', e.target.value)}
                      />
                    </Col>
                    <Col span={2} style={{ textAlign: 'center' }}>
                      ~
                    </Col>
                    <Col span={10}>
                      <Input
                        placeholder="对该枚举项的描述"
                        value={enumItem.description}
                        onChange={e => updateEnumItem(param.id, index, 'description', e.target.value)}
                      />
                    </Col>
                    <Col span={2}>
                      <Button
                        type="text"
                        danger
                        size="small"
                        onClick={() => removeEnumItem(param.id, index)}
                      >
                        删除
                      </Button>
                    </Col>
                  </Row>
                </div>
              ))}
            </div>
          </div>
        );
      case DataType.BOOL:
        return (
          <div className="param-type-fields">
            <Form.Item label="布尔值">
              <div style={{ marginBottom: 16 }}>
                <Row gutter={16} align="middle">
                  <Col span={2} style={{ textAlign: 'center', fontWeight: 'bold' }}>
                    0
                  </Col>
                  <Col span={2} style={{ textAlign: 'center' }}>
                    -
                  </Col>
                  <Col span={8}>
                    <Input
                      placeholder="如：关"
                      value={param.boolMapping?.falseLabel || ''}
                      onChange={e => updateParam(param.id, 'boolMapping', {
                        ...param.boolMapping,
                        falseLabel: e.target.value
                      })}
                    />
                  </Col>
                </Row>
              </div>
              <div>
                <Row gutter={16} align="middle">
                  <Col span={2} style={{ textAlign: 'center', fontWeight: 'bold' }}>
                    1
                  </Col>
                  <Col span={2} style={{ textAlign: 'center' }}>
                    -
                  </Col>
                  <Col span={8}>
                    <Input
                      placeholder="如：开"
                      value={param.boolMapping?.trueLabel || ''}
                      onChange={e => updateParam(param.id, 'boolMapping', {
                        ...param.boolMapping,
                        trueLabel: e.target.value
                      })}
                    />
                  </Col>
                </Row>
              </div>
            </Form.Item>
          </div>
        );
      case DataType.TEXT:
        return (
          <div className="param-type-fields">
            <Form.Item label="数据长度">
              <Input
                placeholder="10240"
                value={param.maxLength}
                onChange={e => updateParam(param.id, 'maxLength', e.target.value)}
                suffix="字节"
              />
            </Form.Item>
          </div>
        );
      case DataType.DATE:
        return (
          <div className="param-type-fields">
            <Form.Item label="日期格式">
              <Select
                placeholder="请选择日期格式"
                value={param.format}
                onChange={value => updateParam(param.id, 'format', value)}
                options={[
                  { label: 'YYYY-MM-DD', value: 'YYYY-MM-DD' },
                  { label: 'YYYY-MM-DD HH:mm:ss', value: 'YYYY-MM-DD HH:mm:ss' },
                  { label: 'HH:mm:ss', value: 'HH:mm:ss' }
                ]}
              />
            </Form.Item>
          </div>
        );
      case DataType.ARRAY:
        return (
          <div className="param-type-fields">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="元素类型">
                  <Row gutter={8}>
                    {arrayElementTypeOptions.map(option => (
                      <Col key={option.value} span={4}>
                        <Button
                          type={param.arrayElementType === option.value ? 'primary' : 'default'}
                          size="small"
                          onClick={() => updateParam(param.id, 'arrayElementType', option.value)}
                          style={{ width: '100%' }}
                        >
                          {option.label}
                        </Button>
                      </Col>
                    ))}
                  </Row>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="元素个数">
                  <InputNumber
                    placeholder="10"
                    value={param.arraySize}
                    onChange={value => updateParam(param.id, 'arraySize', value)}
                    style={{ width: '100%' }}
                    min={1}
                  />
                </Form.Item>
              </Col>
            </Row>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="param-editor">
      <div className="param-header">
        <h3>输入参数</h3>
        <Button type="primary" icon={<PlusOutlined />} onClick={addParam}>
          新增参数
        </Button>
      </div>

      {params.length === 0 ? (
        <div className="no-params">请点击"新增参数"按钮添加参数</div>
      ) : (
        params.map((param, index) => (
          <div key={param.id} className="param-item">
            <Divider orientation="left">参数 {index + 1}</Divider>
            <Row gutter={24}>
              <Col span={8}>
                <Form.Item
                  label="参数名称"
                  required
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                >
                  <Input
                    placeholder="请输入参数名称"
                    value={param.paramName}
                    onChange={e => updateParam(param.id, 'paramName', e.target.value)}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="标识符"
                  required
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                >
                  <Input
                    placeholder="请输入标识符"
                    value={param.paramKey}
                    onChange={e => updateParam(param.id, 'paramKey', e.target.value)}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="数据类型"
                  required
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                >
                  <Select
                    placeholder="请选择数据类型"
                    value={param.dataType}
                    onChange={value => updateParam(param.id, 'dataType', value)}
                    options={dataTypeOptions}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={16}>
                <Form.Item
                  label="参数描述"
                  labelCol={{ span: 4 }}
                  wrapperCol={{ span: 20 }}
                >
                  <Input.TextArea
                    placeholder="请输入"
                    rows={2}
                    maxLength={100}
                    showCount
                    value={param.description}
                    onChange={e => updateParam(param.id, 'description', e.target.value)}
                  />
                </Form.Item>
              </Col>
              <Col span={8} className="param-actions">
                <Form.Item label="必填" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                  <Switch
                    checked={param.required}
                    onChange={checked => updateParam(param.id, 'required', checked)}
                  />
                </Form.Item>
                <Space>
                  <Tooltip title="查看详情">
                    <Button
                      type="text"
                      icon={<EyeOutlined />}
                      onClick={() => viewParam(param)}
                    />
                  </Tooltip>
                  <Tooltip title="删除参数">
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => removeParam(param.id)}
                    />
                  </Tooltip>
                </Space>
              </Col>
            </Row>
            {renderParamFields(param)}
          </div>
        ))
      )}
    </div>
  );
};

export default ParamEditor;
