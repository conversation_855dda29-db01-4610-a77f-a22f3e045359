import { request } from '@/fetch/core';
import { HttpStatusCode } from '@/fetch/core/constant';
import { Select, Table } from 'antd';
import React, { useState } from 'react';

const ProductPackage = (props: {
  versionList: any[];
  productType: string;
  form: any;
}) => {
  const [tableList, setTableList] = useState<any[]>([]);
  const columns: any[] = [
    {
      title: '应用',
      dataIndex: 'appName',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '版本号',
      dataIndex: 'version',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '更新内容',
      dataIndex: 'updateInfo',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '缺陷',
      dataIndex: 'defect',
      align: 'center',
      fixed: 'left',
    },
  ];
  return (
    <>
      <Select
        options={props.versionList}
        placeholder="请选择产品包"
        onChange={(value: any) => {
          props.form.setFieldValue('productPackageNumber', value);
          request({
            path: '/ota/web/get_product_package_detail_info',
            method: 'POST',
            body: {
              productPackageNumber: value,
            },
          })
            .then((res: any) => {
              if (res?.code === HttpStatusCode.Success) {
                setTableList(res?.data?.appInfoList || []);
              }
            })
            .catch((e) => {});
        }}
      ></Select>
      <Table columns={columns} dataSource={tableList}></Table>
    </>
  );
};
export default ProductPackage;
