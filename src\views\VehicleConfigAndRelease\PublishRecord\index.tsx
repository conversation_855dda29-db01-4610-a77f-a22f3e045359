import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { formatLocation } from '@/utils/formatLocation';
import { Table, Tabs, Form } from 'antd';
import { CustomButton, ButtonType } from '@/components/CustomButton';
import FormTitle from '@/components/FormTitle';
import BreadCrumb from '@/components/BreadCrumb';
import {
  PublishTabType,
  publishTabMenu,
  vehicleStatue,
} from '../utils/constant';
import {
  pageSizeOptions,
  SearchCondition,
  TableListType,
} from '@/utils/constant';
import Searchform from '@/components/Searchform';
import {
  AppInfoColumns,
  PublishRecordSearchConfig,
  PublishRecordTableColumns,
  currentVersionColumns,
} from '../utils/columns';
import { api } from '@/fetch/core/api';
import './index.scss';
import { request } from '@/fetch/core';
import { HttpStatusCode } from '@/fetch/core/constant';
import BatchDiffModal from '@/components/BatchDiffModal';
const { TabPane } = Tabs;
const BreadCrumbItemsMap = new Map([
  [
    'publishRecord',
    [
      { title: 'OTA管理', route: '' },
      { title: '车辆配置与升级', route: '' },
      { title: '版本信息', route: '' },
    ],
  ],
]);
const PublishRecord = () => {
  const navigator = useNavigate();
  const [formRef] = Form.useForm();
  const urlData = formatLocation(window.location.search);
  const [activeKey, setActiveKey] = useState<string>(
    PublishTabType.CURRENT_VERSION,
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [dropDownList, setDropDownList] = useState<any>();
  const [currentVersion, setCurrentVersion] = useState<any>(null);
  const [diffModalOpen, setDiffModalOpen] = useState(false);
  const [selectedIssueNumber, setSelectedIssueNumber] = useState('');
  const [tableList, setTableList] = useState<TableListType>({
    list: [],
    totalPage: 0,
    totalNumber: 0,
  });
  const [searchCondition, setSearchCondition] = useState<SearchCondition>({
    searchForm: {
      issueTaskNumber: null,
      appName: null,
      contemporaryVersionNumber: null,
      latestIssueTaskResult: null,
    },
    current: 1,
    pageSize: 10,
  });

  /**
   * 获取发布记录table信息
   * @param {Object} searchValues 搜索条件
   * @param {string} issueType 获取的发布记录类型
   */
  const fetchTableListData = (searchValues: any, issueType: string) => {
    setLoading(true);
    try {
      request({
        method: 'POST',
        path: api.getIssueTaskHistoryList,
        body: {
          vehicleName: urlData.vehicleName,
          issueType: issueType,
          issueTaskNumber: searchValues.searchForm?.issueTaskNumber,
          appName: searchValues.searchForm?.appName?.value,
          contemporaryVersion:
            searchValues.searchForm?.contemporaryVersionNumber,
          status: searchValues.searchForm?.latestIssueTaskResult?.value,
          pageNum: searchValues.current,
          pageSize: searchValues.pageSize
        }
      }).then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setTableList({
            list: res.data.list,
            totalPage: res.data.pages,
            totalNumber: res.data.total,
          });
        }
      });
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取发布模块下拉列表
   */
  const fetchApplicationInfoList = () => {
    return new Promise((resolve: any, reject: any) => {
      request({
        method: 'POST',
        path: api.getApplicationInfoList,
      })
        .then((res: any) => {
          if (res && res.code === HttpStatusCode.Success) {
            setDropDownList({
              appName: res.data?.map((item: any) => {
                return { label: item.name, value: item.code };
              }),
            });
          }
        })
        .catch((err) => {
          console.log(err);
        });
    });
  };

  /**
   * 下载
   * @param id
   */
  const downLoad = (id: any) => {
    request({
      method: 'POST',
      path: api.downloadIssueModule,
      body: {
        id: id,
      },
    })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          const newUrl = res.data.url.replace('http', 'https');
          window.open(newUrl, '_self');
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const formatColumns = () => {
    return PublishRecordTableColumns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${
              (searchCondition.current - 1) * searchCondition.pageSize +
              index +
              1
            }`;
          break;
        case 'statusName':
          col.render = (item: any, record: any) => {
            if (record.status === vehicleStatue.FAILURE) {
              return <div style={{ color: 'red' }}>{item}</div>;
            } else {
              return <div>{item}</div>;
            }
          };
          break;
        case 'operate':
          col.render = (item: any, record: any) => {
            if (record.appName !== 'map') {
              if (record.appName === 'rover-conf') {
                return (
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                  >
                    <a
                      onClick={() => downLoad(record.id)}
                      style={{ marginRight: '10px' }}
                    >
                      下载
                    </a>
                    <a
                      onClick={() => {
                        setDiffModalOpen(true);
                        setSelectedIssueNumber(record.issueTaskNumber);
                      }}
                    >
                      版本对比
                    </a>
                  </div>
                );
              }
              return <a onClick={() => downLoad(record.id)}>下载</a>;
            } else {
              return '--';
            }
          };
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };

  /**
   * 切换页签
   * @param {string} key 页签标识符
   */
  const onTabsChange = (key: string) => {
    setActiveKey(key);
    const search = {
      searchForm: {
        issueTaskNumber: null,
        appName: null,
        contemporaryVersionNumber: null,
        latestIssueTaskResult: null,
      },
      current: 1,
      pageSize: 10,
    };
    setTableList({
      list: [],
      totalPage: 0,
      totalNumber: 0,
    });
    fetchTableListData(search, key);
  };

  const onSearchClick = () => {
    const data = {
      searchForm: formRef.getFieldsValue(),
      pageSize: 10,
      current: 1,
    };
    setSearchCondition(data);
    fetchTableListData(data, activeKey);
  };

  const onResetClick = () => {
    const search = {
      searchForm: {
        issueTaskNumber: null,
        appName: null,
        contemporaryVersionNumber: null,
        latestIssueTaskResult: null,
      },
      current: 1,
      pageSize: 10,
    };
    setSearchCondition(search);
    formRef.setFieldsValue(search.searchForm);
    fetchTableListData(search, activeKey);
  };

  const getCurrentVesionInfo = () => {
    request({
      path: '/ota/web/get_vehicle_current_version',
      method: 'POST',
      body: {
        vehicleName: urlData.vehicleName,
      },
    })
      .then((res: any) => {
        if (res?.code === HttpStatusCode.Success) {
          setCurrentVersion(res?.data);
        }
      })
      .catch((e) => {});
  };

  useEffect(() => {
    getCurrentVesionInfo();
    fetchApplicationInfoList();
  }, []);
  return (
    <div className="publish-record">
      <BreadCrumb items={BreadCrumbItemsMap.get('publishRecord')} />
      <div className="content">
        <FormTitle title={`${urlData.vehicleName}版本信息`} />
        <div className="tab-title">
          <Tabs activeKey={activeKey} onChange={onTabsChange}>
            {publishTabMenu?.map((item: any) => {
              return <TabPane tab={item.name} key={item.key}></TabPane>;
            })}
          </Tabs>
        </div>
        {activeKey === PublishTabType.CURRENT_VERSION ? (
          <div className="current-version">
            <div className="label">
              <i>车牌号：{urlData.vehicleName}</i>
              <i style={{ marginLeft: '8px' }}>
                车架号：{currentVersion?.serialNo}
              </i>
            </div>
            <div className="label">
              当前产品包版本：{currentVersion?.productPackageVersion}
            </div>
            <Table
              columns={currentVersionColumns}
              dataSource={currentVersion?.appInfoList || []}
            ></Table>
            <div className="label">其他应用版本</div>
            <Table
              columns={AppInfoColumns}
              dataSource={currentVersion?.subAppInfoList || []}
            ></Table>
          </div>
        ) : (
          <div>
            <div className="tab-searchform">
              <Searchform
                dropDownMap={dropDownList}
                configData={PublishRecordSearchConfig}
                onSearchClick={onSearchClick}
                onResetClick={onResetClick}
                initValues={searchCondition.searchForm}
                formRef={formRef}
              />
            </div>
            <div className="tab-content">
              <Table
                rowKey={(record) => record.id}
                loading={loading}
                bordered
                dataSource={tableList.list}
                columns={formatColumns()}
                scroll={{
                  y: 500,
                }}
                pagination={{
                  position: ['bottomCenter'],
                  total: tableList.totalNumber,
                  current: searchCondition.current,
                  pageSize: searchCondition.pageSize,
                  showQuickJumper: true,
                  showSizeChanger: true,
                  pageSizeOptions: pageSizeOptions,
                  showTotal: (total) =>
                    `共 ${tableList.totalPage}页,${total} 条记录`,
                }}
                onChange={(
                  paginationData: any,
                  filters: any,
                  sorter: any,
                  extra: any,
                ) => {
                  if (extra.action === 'paginate') {
                    const { current, pageSize } = paginationData;
                    const newSearchValue = {
                      ...searchCondition,
                      current,
                      pageSize,
                    };
                    setSearchCondition(newSearchValue);
                    fetchTableListData(newSearchValue, activeKey);
                  }
                }}
              />
            </div>
          </div>
        )}
        <div className="submit-btns">
          <CustomButton
            buttonType={ButtonType.DefaultButton}
            onSubmitClick={() => navigator('/vehicleConfigAndRelease')}
            title={'返回'}
          />
        </div>
      </div>
      <BatchDiffModal
        diffModalOpen={diffModalOpen}
        onCancel={() => {
          setDiffModalOpen(false);
          setSelectedIssueNumber('');
        }}
        deviceName={urlData.vehicleName}
        productKey="rover"
        selectedIssueNumber={selectedIssueNumber}
      />
    </div>
  );
};

export default React.memo(PublishRecord);
