import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Table, message } from 'antd';
import {
  pageSizeOptions,
  ProductType,
  SearchCondition,
  TableListType,
  UpgradeTypeMap,
} from '@/utils/constant';
import {
  saveSearchValues,
  removeSearchValues,
  searchformSelector,
} from '@/redux/reducers/searchform';
import { saveSelectedVehicle } from '@/redux/reducers/selectedVehicle';
import { TableColumns, searchConfig } from './utils/columns';
import { api } from '@/fetch/core/api';
import {
  VehicleConfIssueStatus,
  LatestIssueTaskResultStatus,
} from './utils/constant';
import './index.scss';
import { request } from '@/fetch/core';
import { formatLocation } from '@/utils/formatLocation';
import { HttpStatusCode } from '@/fetch/core/constant';
import { saveVehicleConfigContent } from '@/redux/reducers/vehicleConfig';
import { TableBtnBuried, BtnBuried } from '@/components/BtnBuried';
import { VehicleConfigAndReleaseFetch } from './utils/fetch';
import CommonForm from '@/components/CommonForm';
import { FieldItem, FormConfig } from '@/components/CommonForm/formConfig';
import { cloneDeep } from 'lodash';

const VehicleConfigAndRelease = () => {
  const fetchApi = new VehicleConfigAndReleaseFetch();
  const [searchFormConfig, setSearchFormConfig] = useState<FormConfig>(
    cloneDeep(searchConfig),
  );
  const navigator = useNavigate();
  const dispatch = useDispatch();
  const formRef = useRef<any>(null);
  const selectedVehicleInfoRef = useRef<any>(new Map());
  const selectedVehicleRef = useRef<any>(new Set());
  const historySearchValue = useSelector(searchformSelector).searchValues;
  const [loading, setLoading] = useState<boolean>(false);
  const urlData: any = formatLocation(window.location.search);
  const vehicleTypeId = sessionStorage.getItem('vehicleTypeId');
  const vehicleTypeName = sessionStorage.getItem('vehicleTypeName');
  const initSearchCondition = {
    searchForm: {
      stationInfo: null,
      vehicleName: urlData.vehicleName ? urlData.vehicleName : null,
      ownerUseCase: [],
      vehicleTypeId:
        vehicleTypeId && vehicleTypeName
          ? { key: vehicleTypeId, value: vehicleTypeId, label: vehicleTypeName }
          : null,
      vehicleConfTemplate: null,
      vehicleConfIssueStatus: null,
      latestIssueTaskResult: null,
      appName: null,
      version: null,
      pushStreamDevice: null,
      videoCamera: null,
      vehicleBusinessType: null,
    },
    current: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState<SearchCondition>(
    () => {
      return historySearchValue ? historySearchValue : initSearchCondition;
    },
  );
  const [tableList, setTableList] = useState<TableListType>({
    list: [],
    totalPage: 0,
    totalNumber: 0,
  });
  const [tableSelectedRowKeys, setTableSelectedRowKeys] = useState<React.Key[]>(
    [],
  ); // 当前页面已选
  const [dropDownList, setDropDownList] = useState<any>();
  useEffect(() => {
    if (urlData.clear) {
      sessionStorage.removeItem('vehicleTypeId');
      sessionStorage.removeItem('vehicleTypeName');
    }
    fetchTableData(searchCondition);
    fetchAllDropDownList();
  }, []);

  const findFormField = (fieldName: string) => {
    return searchFormConfig?.fields?.find(
      (field: FieldItem) => field.fieldName === fieldName,
    );
  };
  const getHardwareTypeList = () => {
    request({
      path: 'ota/web/get_hardware_type_list',
      method: 'POST',
    })
      .then((res: any) => {
        if (res?.code === HttpStatusCode.Success) {
          const data = res?.data?.map((item: any) => ({
            label: item.hardwareTypeName,
            value: item.hardwareTypeId,
          }));
          const field = findFormField('hardwareTypeName');
          field!.options = data;
          setSearchFormConfig({
            ...searchFormConfig,
          });
        }
      })
      .catch((e) => {});
  };

  const getHardwareModalList = () => {
    const searchValue = formRef.current.getFieldsValue();
    if (!searchValue?.hardwareTypeName?.value) {
      message.error('请先硬件类型！');
      const field = findFormField('hardwareModelId');
      field!.options = [];
      setSearchFormConfig({
        ...searchFormConfig,
      });
      return;
    }

    request({
      path: '/ota/web/get_hardware_model_list',
      method: 'POST',
      body: {
        hardwareTypeId: searchValue?.hardwareTypeName?.value,
      },
    })
      .then((res: any) => {
        if (res?.code === HttpStatusCode.Success) {
          const data = res?.data?.map((item: any) => ({
            label: item.hardwareModelName,
            value: item.hardwareModelId,
          }));
          const field = findFormField('hardwareModelId');
          field!.options = data;
          setSearchFormConfig({
            ...searchFormConfig,
          });
        }
      })
      .catch((e) => {});
  };

  const getBoxTemplateList = () => {
    const productType = formRef.current?.getFieldValue('productType');
    if (!productType?.value) {
      const field = findFormField('boxTemplateId');
      field!.options = [];
      setSearchFormConfig({
        ...searchFormConfig,
      });
      return;
    }
    request({
      path: 'ota/web/get_box_template_list',
      method: 'POST',
      body: {
        productType: productType?.value,
      },
    })
      .then((res: any) => {
        if (res?.code === HttpStatusCode.Success) {
          const data = res?.data?.map((item: any) => ({
            label: item.boxTemplateName,
            value: item.boxTemplateId,
          }));
          const field = findFormField('boxTemplateId');
          field!.options = data;
          setSearchFormConfig({
            ...searchFormConfig,
          });
        }
      })
      .catch((e) => {});
  };

  const getVehicleBusinessType = () => {
    const productType = formRef.current?.getFieldValue('productType');
    const vehicleBusiness = findFormField('vehicleBusinessType');
    if (!productType?.value) {
      vehicleBusiness!.options = [];
      setSearchFormConfig({
        ...searchFormConfig,
      });
      return;
    }
    vehicleBusiness!.options =
      productType?.value === ProductType.VEHICLE
        ? [
            {
              label: '配送车',
              value: 'DISPATCH',
            },
            {
              label: '售卖车',
              value: 'VENDING',
            },
          ]
        : productType?.value === ProductType.ROBOT
        ? [
            {
              label: '京麟',
              value: 'JING_LIN',
            },
            {
              label: '重德',
              value: 'ZHONG_DE',
            },
          ]
        : [];
    setSearchFormConfig({
      ...searchFormConfig,
    });
  };
  const fetchVehicleTypeNameList = async () => {
    try {
      const res: any = await request({
        method: 'POST',
        path: api.getVehicleTypeNameList,
      });
      if (res && res.code === HttpStatusCode.Success) {
        return Promise.resolve(
          res.data?.map((item: any) => {
            return { label: item.name, value: item.code };
          }),
        );
      } else {
        return Promise.reject([]);
      }
    } catch (e) {
      console.log(e);
    }
  };
  const fetchVehicleConfTemplateDropDownList = async () => {
    try {
      const res: any = await request({
        method: 'POST',
        path: api.getVehicleConfTemplateDropDownList,
        body: {},
      });
      if (res && res.code === HttpStatusCode.Success) {
        return Promise.resolve(
          res.data?.map((item: any) => {
            return { label: item.name, value: item.code };
          }),
        );
      } else {
        return Promise.reject([]);
      }
    } catch (e) {
      console.log(e);
    }
  };
  const fetchAllDropDownList = () => {
    Promise.all([
      fetchVehicleTypeNameList(),
      fetchVehicleConfTemplateDropDownList(),
    ]).then((res: any) => {
      if (res?.length === 2) {
        const vehicleTypeId = searchFormConfig?.fields?.find(
          (field: FieldItem) => field.fieldName === 'vehicleTypeId',
        );
        const vehicleConfTemplate = searchFormConfig?.fields?.find(
          (field: FieldItem) => field.fieldName === 'vehicleConfTemplate',
        );
        vehicleTypeId!.options = res[0];
        vehicleConfTemplate!.options = res[1];
      }
      setSearchFormConfig({
        ...searchFormConfig,
      });
    });
  };
  const fetchTableData = async (searchValues: any) => {
    setLoading(true);
    try {
      const { searchForm, current, pageSize } = searchValues;
      const { stationInfo } = searchForm;
      const res: any = await request({
        method: 'POST',
        path: api.getVehicleConfIssuePageList,
        body: {
          provinceId: stationInfo && stationInfo[0],
          cityId: stationInfo && stationInfo[1],
          stationId: stationInfo && stationInfo[2],
          vehicleName: searchForm.vehicleName,
          ownerUseCaseList: searchForm.ownerUseCase?.map((item: any) => {
            return item.value;
          }),
          serialNo: searchForm.serialNo,
          vehicleTypeId: searchForm.vehicleTypeId?.value,
          productType: searchForm.productType?.value,
          hardwareModelId: searchForm.hardwareModelId?.value,
          vehicleConfTemplateNumber: searchForm.vehicleConfTemplate?.value,
          vehicleConfIssueStatus: searchForm.vehicleConfIssueStatus?.value,
          appName: searchForm.appName?.value,
          version: searchForm.version?.label,
          vehicleBusinessType: searchForm.vehicleBusinessType?.value,
          boxTemplateId: searchForm.boxTemplateId?.value,
          pageNum: current,
          pageSize: pageSize,
        },
      }).then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setTableList({
            list: res.data.list,
            totalPage: res.data.pages,
            totalNumber: res.data.total,
          });
          const selectedList: any[] = [];
          res.data.list.filter((item: any) => {
            if (selectedVehicleRef.current.has(item.vehicleName)) {
              selectedList.push(item.vehicleName);
            }
          });
          setTableSelectedRowKeys(selectedList);
        }
      });
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取模块下拉框列表数据
   */
  const fetchApplicationInfoList = () => {
    request({
      method: 'POST',
      path: api.getApplicationInfoList,
    })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          const field = searchFormConfig?.fields?.find(
            (field: FieldItem) => field.fieldName === 'appName',
          );
          if (res?.code === HttpStatusCode.Success) {
            field!.options = res.data?.map((item: any) => {
              return { label: item.name, value: item.code };
            });
          }
          setSearchFormConfig({
            ...searchFormConfig,
          });
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  /**
   * 获取版本号下拉框内容
   * @returns
   */
  const fetchVersionDropDown = async () => {
    const searchValue = formRef.current.getFieldsValue();
    if (!searchValue?.appName?.value) {
      const version = findFormField('version');
      version!.options = [];
      setSearchFormConfig({
        ...searchFormConfig,
      });
      setDropDownList({
        ...dropDownList,
        version: [],
      });
      message.error('请先选择模块！');
      return;
    }

    const res: any = await fetchApi.fetchVersionDropDown({
      appName: searchValue?.appName?.value,
      enable: 1,
    });
    if (res.code === HttpStatusCode.Success && res.data) {
      const field = searchFormConfig?.fields?.find(
        (field: FieldItem) => field.fieldName === 'version',
      );
      if (res?.code === HttpStatusCode.Success) {
        field!.options = res.data?.map((item: any) => {
          return { label: item.version, value: item.versionNumber };
        });
      }
      setSearchFormConfig({
        ...searchFormConfig,
      });
    }
  };

  const rowSelection = {
    selectedRowKeys: tableSelectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
      // 减少已选车辆
      if (selectedRowKeys.length < tableSelectedRowKeys.length) {
        tableSelectedRowKeys?.forEach((item: any) => {
          if (selectedRowKeys.indexOf(item) === -1) {
            selectedVehicleRef.current.delete(item);
            selectedVehicleInfoRef.current.delete(item);
          }
        });
      } else {
        selectedRows?.forEach((item: any) => {
          selectedVehicleInfoRef.current.set(item.vehicleName, item);
          selectedVehicleRef.current.add(item.vehicleName);
        });
      }
      setTableSelectedRowKeys(selectedRowKeys);
    },
  };
  const formatColumns = () => {
    return TableColumns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${
              (searchCondition.current - 1) * searchCondition.pageSize +
              index +
              1
            }`;
          break;
        case 'latestIssueTaskResult':
          col.render = (item: any, record: any) => {
            if (
              record.latestIssueTaskResult ===
              LatestIssueTaskResultStatus.TO_UPGRADE
            ) {
              return (
                <div style={{ color: 'red' }}>
                  {record.latestIssueTaskResultName}
                </div>
              );
            } else {
              return <div>{record.latestIssueTaskResultName ?? '-'}</div>;
            }
          };
          break;
        case 'operate':
          col.render = (item: any, record: any) => {
            return (
              <div className="operate-btn">
                <TableBtnBuried
                  title="车辆配置"
                  handleClick={() => {
                    dispatch(
                      saveSearchValues({
                        routeName: location.pathname,
                        searchValues: searchCondition,
                      }),
                    );
                    navigator(
                      '/vehicleConfigAndRelease/vehicleConf?type=single&vehicleName=' +
                        record.vehicleName +
                        '&productType=' +
                        record.productType,
                    );
                    dispatch(
                      saveVehicleConfigContent({
                        vehicleType: record.vehicleTypeName,
                        vehicleTypeId: record.vehicleTypeId,
                        vehicleConfTemplateNumber:
                          record.vehicleConfTemplateNumber,
                        vehicleConfTemplateName: record.vehicleConfTemplateName,
                      }),
                    );
                    selectedVehicleInfoRef.current.clear();
                    selectedVehicleRef.current.clear();
                  }}
                  show={
                    record.vehicleConfIssueStatus !==
                    VehicleConfIssueStatus.VEHICLE_TYPE_NOT_CONFIGURE
                  }
                  clstagKey="vehicleConfigAndRelease_online_1682322223871|11"
                />
                <TableBtnBuried
                  title="配置操作记录"
                  handleClick={() => {
                    dispatch(
                      saveSearchValues({
                        routeName: location.pathname,
                        searchValues: searchCondition,
                      }),
                    );
                    navigator(
                      '/vehicleConfigAndRelease/confRecord?vehicleName=' +
                        record.vehicleName,
                    );
                    selectedVehicleInfoRef.current.clear();
                    selectedVehicleRef.current.clear();
                  }}
                  show={true}
                  clstagKey="vehicleConfigAndRelease_online_1682322223871|12"
                />
                <TableBtnBuried
                  title="版本信息"
                  handleClick={() => {
                    dispatch(
                      saveSearchValues({
                        routeName: location.pathname,
                        searchValues: searchCondition,
                      }),
                    );
                    navigator(
                      '/vehicleConfigAndRelease/publishRecord?vehicleName=' +
                        record.vehicleName,
                    );
                    selectedVehicleInfoRef.current.clear();
                    selectedVehicleRef.current.clear();
                  }}
                  show={true}
                  clstagKey="vehicleConfigAndRelease_online_1682322223871|13"
                />
                <TableBtnBuried
                  title="查看发布计划"
                  handleClick={() => {
                    sessionStorage.setItem(
                      'latestIssueTaskNumber',
                      record.latestIssueTaskNumber,
                    );
                    navigator(
                      '/releasePlanManage?number=' +
                        record.latestIssueTaskNumber,
                    );
                    selectedVehicleInfoRef.current.clear();
                    selectedVehicleRef.current.clear();
                  }}
                  show={record.latestIssueTaskNumber}
                  clstagKey="vehicleConfigAndRelease_online_1682322223871|14"
                />
              </div>
            );
          };
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };
  const onBatchVehicleConf = () => {
    if (selectedVehicleInfoRef.current.size > 0) {
      const issueStatusSet = new Set(); // 判断配置发布状态用
      const vehicleTypeSet = new Set(); // 判断车型用
      const vehicleConfTemplateSet = new Set(); // 判断车辆配置模板用
      const vehicleTypeIdSet = new Set();
      selectedVehicleInfoRef.current?.forEach(
        (value: any, key: any, map: any) => {
          vehicleTypeSet.add(value.vehicleTypeName);
          vehicleTypeIdSet.add(value.vehicleTypeId);
          vehicleConfTemplateSet.add(value.vehicleConfTemplateNumber);
          if (
            value.vehicleConfIssueStatus ===
            VehicleConfIssueStatus.VEHICLE_TYPE_NOT_CONFIGURE
          ) {
            issueStatusSet.add(value.vehicleConfIssueStatus);
          }
        },
      );
      if (issueStatusSet.size > 0) {
        message.error('已选车辆存在配置发布状态为“车型未配置”，不允许批量！');
        return;
      }
      if (vehicleTypeSet.size > 1) {
        message.error('已选车辆存在多个车型，不允许批量！');
        return;
      }
      if (vehicleConfTemplateSet.size > 1) {
        message.error(
          '已选车辆的车辆配置模板均为空、或者均为同一个车辆配置模板，才允许批量！',
        );
      } else {
        selectedVehicleInfoRef.current?.forEach(
          (value: any, key: any, map: any) => {
            map.set(key, {
              ...value,
              upgradeType: UpgradeTypeMap.get('SILENT')?.key,
              upgradeTypeName: UpgradeTypeMap.get('SILENT')?.name,
            });
          },
        );
        dispatch(saveSelectedVehicle(selectedVehicleInfoRef.current));
        dispatch(
          saveVehicleConfigContent({
            vehicleType: Array.from(vehicleTypeSet)[0],
            vehicleTypeId: Array.from(vehicleTypeIdSet)[0],
            vehicleConfTemplateNumber: Array.from(vehicleConfTemplateSet)[0],
          }),
        );
        navigator('/vehicleConfigAndRelease/vehicleConf?type=batch');
      }
    } else {
      message.error(
        '请至少选择一条配置发布状态为“车型已配置”的车辆，进行批量配置！',
      );
    }
  };
  const onRelease = () => {
    if (selectedVehicleInfoRef.current.size > 0) {
      let vehicleList: any = [];
      let robotList: any = [];
      selectedVehicleInfoRef.current?.forEach(
        (value: any, key: any, map: any) => {
          if (value.productType === ProductType.VEHICLE) {
            vehicleList.push(value);
          } else {
            robotList.push(value);
          }
          map.set(key, {
            ...value,
            upgradeType: UpgradeTypeMap.get('SILENT')?.key,
            upgradeTypeName: UpgradeTypeMap.get('SILENT')?.name,
          });
        },
      );
      if (
        (vehicleList.length > 0 &&
          vehicleList.length < selectedVehicleInfoRef.current.size) ||
        (robotList.length > 0 &&
          robotList.length < selectedVehicleInfoRef.current.size)
      ) {
        message.warning('请选择同一类型产品');
        return;
      }
      dispatch(saveSelectedVehicle(selectedVehicleInfoRef.current));
      navigator('/vehicleConfigAndRelease/releaseConfSoftware');
    } else {
      message.error('请至少选中一条车辆进行发布！');
    }
  };
  const onSearchClick = () => {
    const values = formRef.current.getFieldsValue();
    if (values.appName?.value && !values.version?.value) {
      message.error('版本号不能为空！');
      return;
    }
    const data = {
      searchForm: values,
      pageSize: 10,
      current: 1,
    };
    setSearchCondition(data);
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: data,
      }),
    );
    selectedVehicleInfoRef.current.clear();
    selectedVehicleRef.current.clear();
    fetchTableData(data);
  };
  const onResetClick = () => {
    const search = {
      searchForm: {
        country: null,
        province: null,
        city: null,
        station: null,
        vehicleName: null,
        ownerUseCase: [],
        vehicleType: null,
        vehicleConfTemplate: null,
        vehicleConfIssueStatus: null,
        latestIssueTaskResult: null,
        appName: null,
        version: null,
        pushStreamDevice: null,
        videoCamera: null,
        vehicleBusinessType: null,
        productType: null,
        stationInfo: null,
        hardwareTypeName: null,
        hardwareModelId: null,
        vehicleTypeId: null,
        serialNo: null,
        boxTemplateId: null,
      },
      current: 1,
      pageSize: 10,
    };
    formRef.current.setFieldsValue(search.searchForm);
    dispatch(
      removeSearchValues({
        routeName: null,
        searchValues: search,
      }),
    );
    setSearchCondition({ ...search });
    selectedVehicleInfoRef.current.clear();
    selectedVehicleRef.current.clear();
    fetchTableData(search);
    sessionStorage.removeItem('vehicleTypeId');
    sessionStorage.removeItem('vehicleTypeName');
  };
  return (
    <div className="vehicle-config-release">
      <div className="searchform">
        <CommonForm
          formConfig={searchFormConfig}
          formType="search"
          onSearchClick={onSearchClick}
          onResetClick={onResetClick}
          defaultValue={searchCondition.searchForm}
          colon={false}
          layout={'inline'}
          onFieldFocus={(filedName: string, value: any) => {
            filedName === 'appName' && fetchApplicationInfoList();
            filedName === 'version' && fetchVersionDropDown();
            filedName === 'hardwareTypeName' && getHardwareTypeList();
            filedName === 'hardwareModelId' && getHardwareModalList();
            filedName === 'boxTemplateId' && getBoxTemplateList();
            filedName === 'vehicleBusinessType' && getVehicleBusinessType();
          }}
          onValueChange={(values: any, changedName: string) => {
            if (changedName === 'productType') {
              formRef.current?.setFieldValue('vehicleBusinessType', null);
              formRef.current?.setFieldValue('boxTemplateId', null);
            } else if (changedName === 'appName') {
              formRef.current?.setFieldValue('version', null);
            }
          }}
          getFormInstance={(form: any) => {
            formRef.current = form;
          }}
        />
      </div>
      <div className="table-container">
        <div className="middle-btns">
          <BtnBuried
            onSubmitClick={() => onBatchVehicleConf()}
            title="批量初始化车辆配置"
            otherCSSProperties={{ height: 35, marginBottom: 10 }}
            clstagKey="vehicleConfigAndRelease_online_1682322223871|9"
          />
          <BtnBuried
            onSubmitClick={() => onRelease()}
            title="配置及软件发布"
            otherCSSProperties={{
              height: 35,
              marginBottom: 10,
              marginLeft: 20,
            }}
            clstagKey="vehicleConfigAndRelease_online_1682322223871|10"
          />
        </div>
        <Table
          rowKey={(record) => record.vehicleName}
          loading={loading}
          rowSelection={{ ...rowSelection }}
          bordered
          dataSource={tableList.list}
          columns={formatColumns()}
          scroll={{
            y: 500,
          }}
          pagination={{
            position: ['bottomCenter'],
            total: tableList.totalNumber,
            current: searchCondition.current,
            pageSize: searchCondition.pageSize,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: pageSizeOptions,
            showTotal: (total) =>
              `已选${selectedVehicleRef.current.size}条,共 ${tableList.totalPage}页,${total} 条记录`,
          }}
          onChange={(
            paginationData: any,
            filters: any,
            sorter: any,
            extra: any,
          ) => {
            if (extra.action === 'paginate') {
              const { current, pageSize } = paginationData;
              const newSearchValue = {
                ...searchCondition,
                current,
                pageSize,
              };
              dispatch(
                saveSearchValues({
                  routeName: location.pathname,
                  searchValues: newSearchValue,
                }),
              );
              setSearchCondition(newSearchValue);
              fetchTableData(newSearchValue);
            }
          }}
        />
      </div>
    </div>
  );
};

export default React.memo(VehicleConfigAndRelease);
