import React, { useState } from 'react';
import './index.scss';
import { CheckOutlined } from '@ant-design/icons';
import { Button } from 'antd';

const CommonSteps = ({
  stepTipList,
  children,
  onNextCheckList = [],
  onSubmit,
  onCancel,
  currentStep,
}: {
  stepTipList: string[];
  children: JSX.Element[];
  onNextCheckList?: AnyFunc[];
  onSubmit: AnyFunc;
  onCancel?: AnyFunc;
  currentStep?: number;
}) => {
  const [curStep, setCurStep] = useState<number>(currentStep || 0);

  const handleBack = () => {
    setCurStep(curStep - 1);
  };

  const handleNext = async () => {
    const checkFunc = onNextCheckList[curStep];
    if (!checkFunc) {
      return;
    }
    const flag = await checkFunc();
    if (flag) {
      setCurStep(curStep + 1);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      window.history.go(-1);
    }
  };

  const handleSubmit = async () => {
    const cb = () => {
      setCurStep(curStep + 1);
    };
    const flag = await onSubmit(cb);
    if (flag) {
      cb();
    }
  };

  return (
    <div className="common-steps">
      <div className="step-bar">
        {stepTipList.map((v: string, i: number) => {
          const curStatus =
            i < curStep ? 'finished' : i === curStep ? 'cur' : '';
          return (
            <div className="step-container" key={i}>
              <div className="circle-and-title">
                <div className={`circle ${curStatus}`}>
                  {curStatus === 'finished' ? <CheckOutlined /> : `${i + 1}`}
                </div>
                <div className={`title ${curStatus}`}>{v}</div>
              </div>
              {i + 1 < stepTipList.length && (
                <div className={`line ${curStatus}`}></div>
              )}
            </div>
          );
        })}
      </div>
      {children.map((Comp: JSX.Element, i: number) => {
        return (
          <div
            key={`child-${i}`}
            style={{ display: i === curStep ? 'block' : 'none' }}
          >
            {Comp}
          </div>
        );
      })}

      <div className="bottom-btns">
        {![0, stepTipList.length - 1].includes(curStep) && (
          <Button type="primary" key="back" onClick={handleBack}>
            上一步
          </Button>
        )}
        {![stepTipList.length - 2, stepTipList.length - 1].includes(
          curStep,
        ) && (
          <Button type="primary" key="next" onClick={handleNext}>
            下一步
          </Button>
        )}
        {curStep === stepTipList.length - 2 && (
          <Button type="primary" key="submit" onClick={handleSubmit}>
            提交
          </Button>
        )}
        {curStep !== stepTipList.length - 1 && (
          <Button key="cancel" onClick={handleCancel}>
            取消
          </Button>
        )}
      </div>
    </div>
  );
};

export default React.memo(CommonSteps);
