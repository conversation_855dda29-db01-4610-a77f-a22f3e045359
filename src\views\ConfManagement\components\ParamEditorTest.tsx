import React, { useState } from 'react';
import { Card } from 'antd';
import ParamEditor, { ParamItem, DataType } from './ParamEditor';

const ParamEditorTest: React.FC = () => {
  const [params, setParams] = useState<ParamItem[]>([
    {
      id: '1',
      paramName: '温度阈值',
      paramKey: 'temperature_threshold',
      dataType: DataType.INT,
      required: true,
      min: -50,
      max: 100,
      step: 1,
      unit: '°C',
    },
    {
      id: '2',
      paramName: '设备状态',
      paramKey: 'device_status',
      dataType: DataType.ENUM,
      required: true,
      enumValues: [
        { value: '0', description: '离线' },
        { value: '1', description: '在线' },
        { value: '2', description: '故障' },
      ],
    },
    {
      id: '3',
      paramName: '开关状态',
      paramKey: 'switch_status',
      dataType: DataType.BOOL,
      required: true,
      boolMapping: {
        falseLabel: '关',
        trueLabel: '开',
      },
    },
    {
      id: '4',
      paramName: '设备描述',
      paramKey: 'device_description',
      dataType: DataType.TEXT,
      required: false,
      maxLength: 255,
    },
    {
      id: '5',
      paramName: '传感器数据',
      paramKey: 'sensor_data',
      dataType: DataType.ARRAY,
      required: true,
      arrayElementType: 'double',
      arraySize: 10,
    },
  ]);

  const handleParamsChange = (newParams: ParamItem[]) => {
    setParams(newParams);
    console.log('参数更新:', newParams);
  };

  return (
    <div style={{ padding: '20px' }}>
      <Card title="参数编辑器测试" bordered={false}>
        <ParamEditor params={params} onChange={handleParamsChange} />
      </Card>
    </div>
  );
};

export default ParamEditorTest;
