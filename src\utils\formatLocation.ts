

const decodeUriQuery = (value: any) => {
  let decoded = null
  try {
    decoded = decodeURIComponent(value)
  } catch (e) {
    decoded = value
  }
  return decoded
}

const encodeUriQuery = (value: any) => {
  let encoded = null
  try {
    encoded = encodeURIComponent(value)
  } catch (e) {
    encoded = value
  }
  return encoded
}

export const makeUrlQuery = (searchQuery: any) => {
  const paramsInQuery = Object.entries(searchQuery || {})?.map((item: any) => {
    const [k, v] = item;
    return `${k}=${encodeUriQuery(v)}`;
  }).join('&');
  return paramsInQuery;
}

export const formatLocation = (locationSearch: string) => {
  let search = ""
  if (locationSearch.indexOf("?") != -1) {
    search = locationSearch.split('?')[1]
  } else {
    search = locationSearch
  }
  const params = search.split('&')
  const objParams: any = {}
  params?.forEach((item) => {
    const paramItem = item.split('=')
    objParams[paramItem[0]] = decodeUriQuery(paramItem[1])
  })
  return objParams
}

