import { Moment } from 'moment';
import moment from 'moment';
// 格式时间到秒
export const formatTimeToSecond = (time: Moment) => {
  let formatTime = null;
  if (time) {
    formatTime = time.format('YYYY-MM-DD HH:mm:ss');
  }
  return formatTime;
};

// 格式日期到秒
export const formatDateToSecond = (time: any) => {
  let startTime = null;
  let endTime = null;
  if (time && time.length > 0) {
    const startMoment: Moment = time[0];
    if (startMoment) {
      startTime = startMoment.format('YYYY-MM-DD HH:mm:ss');
    }
    const endMoment: Moment = time[1];
    if (endMoment) {
      endTime = endMoment.format('YYYY-MM-DD HH:mm:ss');
    }
  }
  return {
    startTime: startTime,
    endTime: endTime,
  };
};
