import { request } from '@/fetch/core';
import { Method } from '../core/util';

class Device {
  /**
   * 获取全部分组
   * @return {Promise<any>}
   */
  getAllGroupList = async (params: { productKey?: string }): Promise<any> => {
    return await request({
      path: 'intelligent/device/web/group/get_all_group_list',
      method: Method.POST,
      body: params,
      newGeteway: true,
    });
  };

  addDevice = async (params: {
    deviceName: string;
    productModelNo?: string;
    productKey: string;
    remarkName?: string;
    groupNo?: string;
  }): Promise<any> => {
    return request({
      path: '/intelligent/device/web/device/add',
      method: Method.POST,
      body: params,
      newGeteway: true,
    });
  };
  deleteDeviceList = async (
    deviceNo: string,
    productKey: string,
  ): Promise<any> => {
    return await request({
      path: '/intelligent/device/web/device/delete',
      method: Method.POST,
      newGeteway: true,
      body: {
        deviceNo,
        productKey,
      },
    });
  };
  batchModifyGroup = async (
    groupNo: string,
    deviceNames: string,
    productKey: string,
  ): Promise<any> => {
    return await request({
      path: '/intelligent/device/web/device/batch_modify_group',
      method: Method.POST,
      newGeteway: true,
      body: {
        groupNo,
        deviceNames,
        productKey,
      },
    });
  };

  deleteGroup = async (groupNo: string) => {
    return request({
      path: '/intelligent/device/web/group/delete',
      method: Method.POST,
      newGeteway: true,
      body: {
        groupNo,
      },
    });
  };
  /**
   * 批量新增设备接口
   * @param fileBucketName
   * @param fileKey
   * @return {Promise}
   */
  batchAddDevice = async (params: {
    fileBucketName: string;
    fileKey: string;
    productKey: string;
  }): Promise<any> => {
    return await request({
      path: '/intelligent/device/web/device/batch_add',
      method: Method.POST,
      newGeteway: true,
      body: params,
    });
  };

  /**
   * 批量添加日志查询接口
   * @param param - 查询参数对象
   * @param param.pageNum - 当前页码
   * @param param.pageSize - 每页记录数
   * @param param.productKey - 产品Key
   * @param param.startTime - 开始时间
   * @param param.endTime - 结束时间
   * @returns Promise对象，包含查询结果
   */
  queryBatchAddLog = async (param: {
    pageNum: number;
    pageSize: number;
    productKey: any;
    startTime: string;
    endTime: string;
  }): Promise<any> => {
    const { pageNum, pageSize, productKey, startTime, endTime } = param;
    return await request({
      path: '/intelligent/device/web/device/query_batch_add_log',
      method: Method.POST,
      newGeteway: true,
      body: {
        pageNum,
        pageSize,
        productKey: productKey ? productKey.value : '',
        startTime,
        endTime,
      },
    });
  };

  /**
   * 获取分组详情
   * @param groupNo 分组编号
   * @return {Promise}
   */
  getDetailByGroupNo = async (groupNo: string): Promise<any> => {
    return request({
      path: 'intelligent/device/web/group/get_detail',
      method: Method.POST,
      newGeteway: true,
      body: {
        groupNo,
      },
    });
  };

  /**
   * 更新分组信息
   * @param params
   * @return {Promise}
   */
  updateGroupInfo = async (params: {
    productKey: string;
    groupNo: string;
    description: string;
  }) => {
    return request({
      path: 'intelligent/device/web/group/update',
      method: Method.POST,
      newGeteway: true,
      body: {
        productKey: params.productKey,
        groupNo: params.groupNo,
        description: params.description,
      },
    });
  };

  /**
   * 新增分组
   * @param {string} productKey - 产品Key
   * @param {string} parentNo - 父组编号
   * @param {string} name - 组名称
   * @param {string} [description] - 组描述
   * @param {{tagList: string; tagValue: string;}[]} tagList - 标签列表
   * @returns {Promise<any>} - 添加结果
   */
  addGroup = async (params: {
    productKey: string;
    parentNo: string;
    name: string;
    description?: string;
    tagList: {
      tagList: string;
      tagValue: string;
    }[];
  }): Promise<any> => {
    return request({
      path: '/intelligent/device/web/group/add',
      method: Method.POST,
      newGeteway: true,
      body: params,
    });
  };
  /**
   * 查询产品列表
   * @returns Promise对象，包含产品列表信息
   */
  queryProductList = async (): Promise<any> => {
    return request({
      path: 'intelligent/device/web/product/query_list',
      method: Method.POST,
      newGeteway: true,
    });
  };
  /**
   * 获取设备分组信息列表
   * @param {number} pageNum - 当前页码
   * @param {number} pageSize - 每页显示数量
   * @param {string} productKey - 产品key
   * @param {string} groupName - 分组名称
   * @param {string[] | null} tagList - 标签列表
   * @returns {Promise<any>} - 请求响应
   */
  getGroupInfoPageList = async (params: {
    pageNum: number;
    pageSize: number;
    productKey: string;
    groupName: string;
    tagList: string[] | null;
  }): Promise<any> => {
    return request({
      path: '/intelligent/device/web/group/get_group_info_page_list',
      method: Method.POST,
      newGeteway: true,
      body: params,
    });
  };

  /**
   * 获取产品型号列表
   * @param productKey 产品Key
   * @returns Promise对象
   */
  queryModelList = async (productKey: string): Promise<any> => {
    return request({
      path: '/intelligent/device/web/product/query_model_list',
      method: Method.POST,
      newGeteway: true,
      body: {
        productKey,
      },
    });
  };

  queryDeviceList = (params: {
    pageNum: number;
    pageSize: number;
    groupNoList?: string[];
    deviceName?: string;
    productModelNo?: string;
    productKey?: string;
  }): Promise<any> => {
    return request({
      path: '/intelligent/device/web/device/query_list_with_page',
      method: Method.POST,
      body: params,
      newGeteway: true,
    });
  };

  /**
   * 更新标签列表
   * @param {object} params - 更新参数
   * @param {string} params.dataNo - 数据编号（产品是productKey，分组是groupNo）
   * @param {number} params.tagType - 标签类型（产品：1 设备：2 分组：3）
   * @param {object[]} params.tagInfoList - 标签信息列表
   * @param {string} params.tagInfoList.tagKey - 标签键
   * @param {string} params.tagInfoList.tagValue - 标签值
   * @returns {Promise<any>} - 更新结果
   */
  updateTagList = async (params: {
    dataNo: string;
    tagType: number;
    tagInfoList: {
      tagKey: string;
      tagValue: string;
    }[];
  }): Promise<any> => {
    return request({
      path: '/intelligent/device/web/tag/update',
      method: Method.POST,
      body: params,
      newGeteway: true,
    });
  };
}

export default Device;
