import React from 'react';
import { But<PERSON>, Image } from 'antd';
import icon from '../../assets/images/no_permission.png';

const NoPermission = () => {
  return <div className='no-permission' style={{
    backgroundColor: 'white',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    color: '#46AC99',
    fontSize: '26px',
    fontFamily: '微软雅黑 Bold',
    fontWeight: '700',
    height: '100vh',
  }}>
    <Image preview={false} width="130px" style={{ marginTop: 88 }} src={icon} />
    <div style={{ marginTop: "50px" }}>{'404'}</div>
    <div style={{ marginTop: 15 }}> {'Sorry, 您访问的页面不存在了'} </div>
  </div>
}

export default React.memo(NoPermission)