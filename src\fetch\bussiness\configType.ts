import { request } from '@/fetch/core';
import { HTTPResponse } from '@/views/ConfManagement/constants';
import {
  AddConfigTypeParams,
} from '@/types/configType';

/**
 * 配置类管理接口
 * 基于 JoySpace 文档: https://joyspace.jd.com/pages/Lu5YaDISGz4pueP00Wza
 */
class ConfigTypeRequest {
  /**
   * 获取配置类分页列表
   * @param {QueryConfigTypeParams} params 查询参数
   * @return {Promise<HTTPResponse>}
   */
  public getConfigTypePage = (): Promise<HTTPResponse> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: `/intelligent/device/web/config_type/get_config_type_page`,
      newGeteway: true,
    };
    return request(requestOptions) as Promise<HTTPResponse>;
  };
  /**
   * 新增配置类
   * @param {AddConfigTypeParams} params 配置类信息
   * @return {Promise<HTTPResponse>}
   */
  public addConfigType = (params: AddConfigTypeParams): Promise<HTTPResponse> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: `/intelligent/device/web/config_item/add_config_item_type`,
      body: params,
      newGeteway: true,
    };
    return request(requestOptions) as Promise<HTTPResponse>;
  };

  /**
   * 更新配置类
   * @param {AddConfigTypeParams} params 配置类信息
   * @return {Promise<HTTPResponse>}
   */
  public updateConfigType = (params: AddConfigTypeParams): Promise<HTTPResponse> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: `/intelligent/device/web/config_item/edit_config_item_type`,
      body: params,
      newGeteway: true,
    };
    return request(requestOptions) as Promise<HTTPResponse>;
  };

  /**
   * 删除配置类
   * @param {string} typeId 配置类ID
   * @return {Promise<HTTPResponse>}
   */
  public deleteConfigType = (itemTypeNo: string): Promise<HTTPResponse> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: `/intelligent/device/web/config_type/delete_config_type`,
      body: {
        itemTypeNo,
      },
      newGeteway: true,
    };
    return request(requestOptions) as Promise<HTTPResponse>;
  };

  getConfigFilePage = (params: any): Promise<HTTPResponse> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: `/intelligent/device/web/config_file/get_config_file_page`,
      body: params,
      newGeteway: true,
    };
    return request(requestOptions) as Promise<HTTPResponse>;
  };
}

export default ConfigTypeRequest;
