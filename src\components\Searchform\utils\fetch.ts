import { Method } from '@/fetch/core/util';
import { request } from '@/fetch/core';

export class SearchFormFetch {
  /**
   * 公共下拉框接口
   * @param {string[]} params
   * @return
   */
  public getCommonDropDown = async (params: string[]) => {
    const requestParam: RequestOptions = {
      method: Method.POST,
      path: '/ota/web/common_get_down_list',
      body: {
        keyList: params,
      },
    };
    return request(requestParam);
  };

  /**
   * 获取联动下拉框当前点击的下拉框内容
   * @param {object} params 请求参数
   * @param {string} params.level 当前点击的地址级别
   * @param {any[]} params.countryIdList
   * @param {any[]} params.provinceIdList
   * @param {any[]} params.cityIdList
   * @param {string} params.dataLevel
   * @param {number} params.enable
   * @return
   */
  public getCurrentDownList = async (params: {
    level: string;
    countryId: any;
    provinceId: any;
    cityId: any;
    stationId?: any;
    enable?: number;
  }) => {
    const requestParam: RequestOptions = {
      method: Method.POST,
      path: '/ota/web/common_get_current_down_list',
      body: params,
    };
    return request(requestParam);
  };

  /**
   * 联动获取父辈
   * @param params
   * @param {number} params.id
   * @param {string} params.level
   * @return
   */
  public getParentLinked = async (params: { id: number; level: string }) => {
    const requestParam: RequestOptions = {
      method: Method.POST,
      path: '/ota/web/common_get_parent_linked',
      body: params,
    };
    return request(requestParam);
  };
}
