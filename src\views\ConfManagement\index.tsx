import React, { useState, useRef, useEffect } from 'react';
import { Button, message, Tabs } from 'antd';
import { useNavigate } from 'react-router-dom';
import { CommonForm, CommonTable } from '@jd/x-coreui';
import Device from '@/fetch/bussiness/device';
import { HttpStatusCode } from '@/fetch/core/constant';
import { useTableData } from '@/components/CommonTable/useTableData';
import dayjs from 'dayjs';
import {
  formConfig,
  columns,
  SearchCondition,
  ProductOption,
  HTTPResponse,
  configFileFormConfig,
  configFileColumns,
  ConfigFileSearchCondition
} from './constants';
import './index.scss';

const ConfManagement: React.FC = () => {
  // 导航钩子
  const navigate = useNavigate();

  // Tab相关状态
  const [activeTab, setActiveTab] = useState<string>('structured');

  // 表单引用
  const formRef = useRef<any>(null);
  const configFileFormRef = useRef<any>(null);

  // 结构化配置项搜索条件
  const [searchCondition, setSearchCondition] = useState<SearchCondition>({
    pageNum: 1,
    pageSize: 10,
  });

  // 配置文件搜索条件
  const [configFileSearchCondition, setConfigFileSearchCondition] = useState<ConfigFileSearchCondition>({
    pageNum: 1,
    pageSize: 10,
  });

  // 初始化配置项请求实例
  const configItemRequest = useRef(new ConfigItemRequest());

  // 使用 useTableData 获取结构化配置项表格数据
  const { tableData, loading, reloadTable } = useTableData<
    SearchCondition,
    any
  >(
    searchCondition,
    configItemRequest.current.getConfigItemPage,
    'configItemList',
  );

  // 使用 useTableData 获取配置文件表格数据
  // TODO: 等待配置文件API接口实现
  // const { tableData: configFileTableData, loading: configFileLoading, reloadTable: reloadConfigFileTable } = useTableData<
  //   ConfigFileSearchCondition,
  //   any
  // >(
  //   configFileSearchCondition,
  //   configItemRequest.current.getConfigFilePage, // 假设有这个方法
  //   'configFileList',
  // );

  // 临时配置文件数据
  const configFileTableData = {
    list: [
      {
        id: 1,
        configFileNo: 'base_map.conf',
        configFileName: '基础地图配置',
        blockNo: 'control',
        configPath: 'control',
        createUser: 'xuehongwei3',
        createTime: '2024-10-10 10:55:57',
        updateTime: '2024-10-11 16:55:57',
        updater: 'xuehongwei3',
        status: '启用'
      },
      {
        id: 2,
        configFileNo: 'base_map.conf',
        configFileName: '基础地图配置',
        blockNo: 'control',
        configPath: 'control',
        createUser: 'qinyinq22',
        createTime: '2024-10-10 13:55:57',
        updateTime: '2024-10-10 16:55:57',
        updater: 'qinyinq22',
        status: '禁用'
      }
    ],
    total: 2,
    pages: 1
  };
  const configFileLoading = false;
  const reloadConfigFileTable = () => {};

  // 产品列表
  const [productOptions, setProductOptions] = useState<ProductOption[]>([]);
  // 模块列表 - 现在由 linkRules 管理，不再需要状态

  // 获取产品列表
  useEffect(() => {
    fetchProductList();
  }, []);

  // 默认表单值
  const [defaultFormValues, setDefaultFormValues] = useState<any>({});

  // 获取产品列表数据
  const fetchProductList = async () => {
    try {
      const deviceInstance = new Device();
      const res: HTTPResponse = await deviceInstance.queryProductList();

      // 根据 HTTPResponse 结构体处理返回结果
      if (res && res.code === HttpStatusCode.Success && res.data) {
        // 转换数据格式为下拉选项格式
        const options = res.data.map((item: any) => ({
          label: item.productName,
          value: item.productKey,
        }));
        setProductOptions(options);

        // 如果有产品数据，默认选择第一个
        if (options.length > 0) {
          const firstProductKey = options[0].value;
          // 设置默认表单值，触发联动
          setDefaultFormValues({ productKey: firstProductKey });

          // 如果表单实例已经初始化，设置表单值
          if (formRef.current) {
            formRef.current.setFieldsValue({ productKey: firstProductKey });
          }
        }
      } else {
        message.error(res.message || '获取产品列表失败');
      }
    } catch (error) {
      message.error('获取产品列表出错');
    }
  };

  // 不再需要获取模块列表数据的函数
  // 因为我们使用了 linkRules 来实现联动

  // 使用从常量文件导入的 formConfig 和 columns
  // 需要更新 formConfig 中的 productOptions
  const localFormConfig = { ...formConfig };
  // 更新产品选项
  if (localFormConfig.fields && localFormConfig.fields.length > 0) {
    const productField = localFormConfig.fields.find(field => field.fieldName === 'productKey');
    if (productField) {
      productField.options = productOptions;
    }
  }

  // 添加操作列
  const tableColumns = [
    ...columns,
    {
      title: '操作',
      dataIndex: 'operation',
      width: 200,
      render: (_: any, record: any) => (
        <div className="operation-buttons">
          <Button type="link" size="small" onClick={() => handleView(record)}>
            查看
          </Button>
          <Button type="link" size="small" onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => handleToggleStatus(record)}
            style={{ color: record.status === '启用' ? '#ff4d4f' : '#52c41a' }}
          >
            {record.status === '启用' ? '禁用' : '启用'}
          </Button>
          <Button
            type="link"
            size="small"
            danger
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </div>
      ),
    },
  ];

  // 表格数据对象
  const tableListData = {
    list: tableData?.list || [],
    totalNumber: tableData?.total || 0,
    totalPage: tableData?.pages || 1,
  };

  // 查看配置项
  const handleView = (record: any) => {
    console.log('查看配置项:', record);
    message.info(`查看配置项: ${record.itemName}`);
  };

  // 编辑配置项
  const handleEdit = (record: any) => {
    console.log('编辑配置项:', record);
    message.info(`编辑配置项: ${record.itemName}`);
  };

  // 切换状态
  const handleToggleStatus = (record: any) => {
    const newEnable = record.status === '启用' ? 0 : 1;
    const newStatus = record.status === '启用' ? '禁用' : '启用';
    console.log(`将配置项 ${record.itemName} 状态修改为: ${newStatus}`);

    // 调用接口更新状态
    configItemRequest.current
      .updateConfigItemStatus(record.itemNo, newEnable)
      .then((res: any) => {
        if (
          res &&
          (res.code === HttpStatusCode.Success ||
            res.code === 200 ||
            res.code === '200')
        ) {
          message.success(`已${newStatus}配置项: ${record.itemName}`);
          // 重新加载表格数据
          reloadTable();
        } else {
          message.error(res.message || `${newStatus}配置项失败`);
        }
      })
      .catch((error: any) => {
        console.error(`${newStatus}配置项出错:`, error);
        message.error(`${newStatus}配置项出错`);
      });
  };

  // 删除配置项
  const handleDelete = (record: any) => {
    console.log('删除配置项:', record);

    // 调用接口删除配置项
    configItemRequest.current
      .deleteConfigItem(record.itemNo)
      .then((res: any) => {
        if (
          res &&
          (res.code === HttpStatusCode.Success ||
            res.code === 200 ||
            res.code === '200')
        ) {
          message.success(`已删除配置项: ${record.itemName}`);
          // 重新加载表格数据
          reloadTable();
        } else {
          message.error(res.message || '删除配置项失败');
        }
      })
      .catch((error: any) => {
        console.error('删除配置项出错:', error);
        message.error('删除配置项出错');
      });
  };

  // 配置类管理
  const handleConfigTypeManagement = () => {
    console.log('配置类管理');
    navigate('/confManagement/configType');
  };

  // 新建结构化配置项
  const handleCreateStructuredConfig = () => {
    navigate('/confManagement/create');
  };

  // 新建配置文件
  const handleCreateConfigFile = () => {
    console.log('新建配置文件');
    message.info('新建配置文件功能待实现');
  };

  // 配置文件操作处理函数
  const handleConfigFileView = (record: any) => {
    console.log('查看配置文件:', record);
    message.info(`查看配置文件: ${record.configFileName}`);
  };

  const handleConfigFileEdit = (record: any) => {
    console.log('编辑配置文件:', record);
    message.info(`编辑配置文件: ${record.configFileName}`);
  };

  const handleConfigFileToggleStatus = (record: any) => {
    const newStatus = record.status === '启用' ? '禁用' : '启用';
    console.log(`将配置文件 ${record.configFileName} 状态修改为: ${newStatus}`);
    message.info(`${newStatus}配置文件: ${record.configFileName}`);
  };

  const handleConfigFileDelete = (record: any) => {
    console.log('删除配置文件:', record);
    message.info(`删除配置文件: ${record.configFileName}`);
  };

  // 渲染结构化配置项Tab内容
  const renderStructuredConfigTab = () => {
    return (
      <>
        <div className="search-form">
          <CommonForm
            formConfig={localFormConfig}
            layout="inline"
            formType="search"
            colon={false}
            className="conf-search-form"
            labelAlign="right"
            defaultValue={defaultFormValues}
            onSearchClick={() => {
              const values = formRef.current?.getFieldsValue();
              console.log('查询条件:', values);

              // 构建搜索条件
              const newSearchCondition: SearchCondition = {
                pageNum: 1,
                pageSize: searchCondition.pageSize,
                productKey: values.productKey,
                blockNo: values.blockNo,
                itemNo: values.itemNo,
                itemName: values.itemName,
                createUser: values.createUser,
                enable:
                  values.enable === '启用'
                    ? 1
                    : values.enable === '禁用'
                    ? 0
                    : undefined,
              };

              // 处理时间范围
              if (values.createTimeRange && values.createTimeRange.length === 2) {
                newSearchCondition.createTimeStart = dayjs(
                  values.createTimeRange[0],
                ).format('YYYY-MM-DD HH:mm:ss');
                newSearchCondition.createTimeEnd = dayjs(
                  values.createTimeRange[1],
                ).format('YYYY-MM-DD HH:mm:ss');
              }

              // 更新搜索条件
              setSearchCondition(newSearchCondition);
              message.success('查询成功');
            }}
            onResetClick={() => {
              formRef.current?.resetFields();
              // 重置时刷新产品列表
              fetchProductList();
              // 重置搜索条件
              setSearchCondition({
                pageNum: 1,
                pageSize: 10,
              });

              // 重置后，如果有产品数据，默认选择第一个
              if (productOptions.length > 0) {
                const firstProductKey = productOptions[0].value;
                // 设置默认表单值，触发联动
                setTimeout(() => {
                  formRef.current?.setFieldsValue({ productKey: firstProductKey });
                }, 0);
              }
            }}
            getFormInstance={(form: any) => {
              formRef.current = form;
              // 如果表单初始化完成时已有产品数据但还没有选中产品，默认选择第一个
              if (
                productOptions.length > 0 &&
                !form.getFieldValue('productKey')
              ) {
                const firstProductKey = productOptions[0].value;
                // 使用 setTimeout 确保在下一个事件循环中设置值，触发联动
                setTimeout(() => {
                  form.setFieldsValue({ productKey: firstProductKey });
                }, 0);
              }
            }}
          />
        </div>

        <div className="table-container">
          <CommonTable
            middleBtns={[
              {
                title: '配置类管理',
                btnType: 'primary',
                onClick: handleConfigTypeManagement,
              },
              {
                title: '新建结构化配置项',
                btnType: 'primary',
                onClick: handleCreateStructuredConfig,
              },
            ]}
            columns={tableColumns}
            tableListData={tableListData}
            rowKey="id"
            loading={loading}
            searchCondition={searchCondition}
            onPageChange={(paginationData: any) => {
              setSearchCondition({
                ...searchCondition,
                pageNum: paginationData.pageNum,
                pageSize: paginationData.pageSize,
              });
            }}
          />
        </div>
      </>
    );
  };

  // 渲染配置文件Tab内容
  const renderConfigFileTab = () => {
    // 配置文件表单配置
    const localConfigFileFormConfig = { ...configFileFormConfig };
    // 更新产品选项
    if (localConfigFileFormConfig.fields && localConfigFileFormConfig.fields.length > 0) {
      const productField = localConfigFileFormConfig.fields.find(field => field.fieldName === 'productKey');
      if (productField) {
        productField.options = productOptions;
      }
    }

    // 配置文件表格列配置（添加操作列）
    const configFileTableColumns = [
      ...configFileColumns,
      {
        title: '操作',
        dataIndex: 'operation',
        width: 200,
        render: (_: any, record: any) => (
          <div className="operation-buttons">
            <Button type="link" size="small" onClick={() => handleConfigFileView(record)}>
              查看
            </Button>
            <Button type="link" size="small" onClick={() => handleConfigFileEdit(record)}>
              编辑
            </Button>
            <Button
              type="link"
              size="small"
              onClick={() => handleConfigFileToggleStatus(record)}
              style={{ color: record.status === '启用' ? '#ff4d4f' : '#52c41a' }}
            >
              {record.status === '启用' ? '禁用' : '启用'}
            </Button>
            <Button
              type="link"
              size="small"
              danger
              onClick={() => handleConfigFileDelete(record)}
            >
              删除
            </Button>
          </div>
        ),
      },
    ];

    // 配置文件表格数据对象
    const configFileTableListData = {
      list: configFileTableData?.list || [],
      totalNumber: configFileTableData?.total || 0,
      totalPage: configFileTableData?.pages || 1,
    };

    return (
      <>
        <div className="search-form">
          <CommonForm
            formConfig={localConfigFileFormConfig}
            layout="inline"
            formType="search"
            colon={false}
            className="conf-search-form"
            labelAlign="right"
            defaultValue={defaultFormValues}
            onSearchClick={() => {
              const values = configFileFormRef.current?.getFieldsValue();
              console.log('配置文件查询条件:', values);

              // 构建搜索条件
              const newSearchCondition: ConfigFileSearchCondition = {
                pageNum: 1,
                pageSize: configFileSearchCondition.pageSize,
                productKey: values.productKey,
                blockNo: values.blockNo,
                configFileNo: values.configFileNo,
                configFileName: values.configFileName,
                createUser: values.createUser,
                enable:
                  values.enable === '启用'
                    ? 1
                    : values.enable === '禁用'
                    ? 0
                    : undefined,
              };

              // 处理时间范围
              if (values.createTimeRange && values.createTimeRange.length === 2) {
                newSearchCondition.createTimeStart = dayjs(
                  values.createTimeRange[0],
                ).format('YYYY-MM-DD HH:mm:ss');
                newSearchCondition.createTimeEnd = dayjs(
                  values.createTimeRange[1],
                ).format('YYYY-MM-DD HH:mm:ss');
              }

              // 更新搜索条件
              setConfigFileSearchCondition(newSearchCondition);
              message.success('查询成功');
            }}
            onResetClick={() => {
              configFileFormRef.current?.resetFields();
              // 重置搜索条件
              setConfigFileSearchCondition({
                pageNum: 1,
                pageSize: 10,
              });

              // 重置后，如果有产品数据，默认选择第一个
              if (productOptions.length > 0) {
                const firstProductKey = productOptions[0].value;
                setTimeout(() => {
                  configFileFormRef.current?.setFieldsValue({ productKey: firstProductKey });
                }, 0);
              }
            }}
            getFormInstance={(form: any) => {
              configFileFormRef.current = form;
              // 如果表单初始化完成时已有产品数据但还没有选中产品，默认选择第一个
              if (
                productOptions.length > 0 &&
                !form.getFieldValue('productKey')
              ) {
                const firstProductKey = productOptions[0].value;
                setTimeout(() => {
                  form.setFieldsValue({ productKey: firstProductKey });
                }, 0);
              }
            }}
          />
        </div>

        <div className="table-container">
          <CommonTable
            middleBtns={[
              {
                title: '新建配置文件',
                btnType: 'primary',
                onClick: handleCreateConfigFile,
              },
            ]}
            columns={configFileTableColumns}
            tableListData={configFileTableListData}
            rowKey="id"
            loading={configFileLoading}
            searchCondition={configFileSearchCondition}
            onPageChange={(paginationData: any) => {
              setConfigFileSearchCondition({
                ...configFileSearchCondition,
                pageNum: paginationData.pageNum,
                pageSize: paginationData.pageSize,
              });
            }}
          />
        </div>
      </>
    );
  };

  // Tab配置
  const tabItems = [
    {
      key: 'structured',
      label: '结构化配置项',
      children: renderStructuredConfigTab(),
    },
    {
      key: 'configFile',
      label: '配置文件',
      children: renderConfigFileTab(),
    },
  ];

  return (
    <div className="conf-management">
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        className="conf-management-tabs"
      />
    </div>
  );
};

export default ConfManagement;
