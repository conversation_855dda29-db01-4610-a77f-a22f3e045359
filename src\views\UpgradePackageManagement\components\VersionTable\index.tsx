import React, { useEffect, useState } from 'react';
import { VersionTableData } from '../AppTable/columns';
import { Modal, Table, message } from 'antd';
import { request } from '@/fetch/core';
import { HttpStatusCode } from '@/fetch/core/constant';
const VersionTable = (props: {
  appName: string;
  versionNumber: string;
  editPackage: AnyFunc;
}) => {
  const { appName, versionNumber, editPackage } = props;
  const [tableList, setTableList] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState<{
    current: number;
    pageSize: number;
  }>({
    current: 1,
    pageSize: 10,
  });
  const [pagination, setPagination] = useState<{
    total: number;
    totalPage: number;
  }>({
    total: 0,
    totalPage: 0,
  });

  const getDownLoadUrl = (versionNumber: string) => {
    request({
      path: '/ota/web/get_package_download_url',
      method: 'POST',
      body: {
        versionNumber,
      },
    }).then((res: any) => {
      if (res.code === HttpStatusCode.Success) {
        res?.data?.forEach((item: any) => {
          const newUrl = item.url?.replace('http', 'https');
          window.open(newUrl, '_self');
        });
      }
    });
  };

  const onDisabled = (record: any) => {
    Modal.confirm({
      content: (
        <p style={{ wordBreak: 'break-all' }}>
          {record.enable
            ? '确定要禁用此版本吗，禁用后此版本不能创建升级任务，也不能生成产品包'
            : '确定要启用此应用吗，启用后此应用可以组成产品包'}
        </p>
      ),
      onCancel: () => {},
      onOk() {
        request({
          method: 'POST',
          path: '/ota/web/change_app_version_status',
          body: {
            versionNumber: record.versionNumber,
            enable: record.enable == 0 ? 1 : 0,
          },
        })
          .then((res: any) => {
            if (res.code === HttpStatusCode.Success) {
              message.success('操作成功');
              getVersionList(currentPage);
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
    });
  };
  const formatColumns = (columns: any[]) => {
    return columns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'operate':
          col.render = (text: any, record: any) => {
            return (
              <div className="btn-group">
                <a
                  style={{
                    color: record.enable ? '#1677ff' : 'gray',
                  }}
                  onClick={() => {
                    editPackage(record, 'edit');
                  }}
                >
                  编辑
                </a>
                <a
                  onClick={() => {
                    getDownLoadUrl(record.versionNumber);
                  }}
                >
                  下载应用包
                </a>
                <a
                  style={{
                    color: record.enable ? '#1677ff' : 'red',
                  }}
                  onClick={onDisabled.bind(null, record)}
                >
                  {record.enable ? '禁用' : '启用'}
                </a>
              </div>
            );
          };
          break;
      }
      return col;
    });
  };

  const getVersionList = (currentPage: {
    current: number;
    pageSize: number;
  }) => {
    request({
      path: '/ota/web/application_version_info_get_page_list',
      method: 'POST',
      body: {
        appName,
        versionNumber,
        pageNum: currentPage.current,
        pageSize: currentPage.pageSize
      },
    })
      .then((res: any) => {
        if (res?.code === HttpStatusCode.Success) {
          setTableList(res?.data?.list);
          setPagination({
            total: res?.data?.total,
            totalPage: res?.data?.pages,
          });
        } else {
          message.warning('获取数据失败');
        }
      })
      .catch((e) => {
        message.error(e.message);
      });
  };
  useEffect(() => {
    getVersionList(currentPage);
  }, [appName, versionNumber]);
  return (
    <Table
      columns={formatColumns(VersionTableData)}
      dataSource={tableList}
      scroll={{
        y: 600,
      }}
      pagination={{
        position: ['bottomCenter'],
        total: pagination.total,
        current: currentPage.current,
        pageSize: currentPage.pageSize,
        showQuickJumper: false,
        showSizeChanger: false,
        pageSizeOptions: [100],
        showTotal: (total) =>
          `共 ${pagination.totalPage}页,${pagination.total} 条记录`,
      }}
      onChange={(
        paginationData: any,
        filters: any,
        sorter: any,
        extra: any,
      ) => {
        if (extra.action === 'paginate') {
          const { current, pageSize } = paginationData;
          setCurrentPage({
            current,
            pageSize,
          });
          getVersionList({ current, pageSize });
        }
      }}
    />
  );
};

export default VersionTable;
