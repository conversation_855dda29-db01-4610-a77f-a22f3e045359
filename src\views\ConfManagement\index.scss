.conf-management {
  padding: 20px;

  .conf-management-tabs {
    .ant-tabs-nav {
      margin-bottom: 20px;
    }

    .ant-tabs-tab {
      font-size: 16px;
      font-weight: 500;
    }

    .ant-tabs-content-holder {
      .ant-tabs-content {
        .ant-tabs-tabpane {
          padding: 0;
        }
      }
    }
  }

  .search-form {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);

    .conf-search-form {
      .ant-form-item {
        margin-right: 16px;
        margin-bottom: 16px;
      }

      .ant-form-item-label {
        padding-right: 8px;
      }

      .ant-select, .ant-input, .ant-picker {
        width: 100%;
      }

      .ant-btn {
        margin-left: 8px;
      }

      // 调整日期范围选择器的宽度
      .ant-picker-range {
        width: 100%;
      }

      // 调整按钮的位置
      .ant-form-item-control-input-content {
        display: flex;
        justify-content: flex-end;
      }
    }
  }

  .table-actions {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
  }

  .table-container {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    padding: 20px;

    .operation-buttons {
      display: flex;
      justify-content: space-around;

      button {
        padding: 0 5px;
      }
    }
  }
}
