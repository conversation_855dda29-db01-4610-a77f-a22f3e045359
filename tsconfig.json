{
  "compilerOptions": {
    "downlevelIteration": true,
    "target": "es5",
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "paths": {
      "@/*": [
        "./src/*"
      ],
    },
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": false,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    // "noEmit": true,
    "jsx": "react",
    "declaration": false,
    "emitDeclarationOnly": false,
    "noImplicitAny": false,

  },
  "include": [
    "src",
  ],
  "exclude": [
    "node_modules",
    "build",
    "dist",
    "src/public-path.js"
  ]
}