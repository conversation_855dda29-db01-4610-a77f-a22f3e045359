import React from 'react';
import { Navigate } from 'react-router-dom';
import MainLayout from '@/layout/MainLayout';
const ConfigManagement = React.lazy(() =>
  _lazyLoad('@/views/ConfigManagement'),
);
const VehicleConfigManage = React.lazy(() =>
  _lazyLoad('@/views/VehicleConfigManage'),
);
const VehicleTypeManage = React.lazy(() =>
  _lazyLoad('@/views/VehicleTypeManage'),
);
const VehicleConfigAndRelease = React.lazy(() =>
  _lazyLoad('@/views/VehicleConfigAndRelease'),
);
const ProductManage = React.lazy(() => _lazyLoad('@/views/ProductManage'));
const AddProduct = React.lazy(() =>
  _lazyLoad('@/views/ProductManage/AddProduct'),
);
const TSLModel = React.lazy(() => _lazyLoad('@/views/ProductManage/TSLModel'));
const TSLDraft = React.lazy(() => _lazyLoad('@/views/ProductManage/TSLDraft'));
const EditAddTSL = React.lazy(() =>
  _lazyLoad('@/views/ProductManage/EditAddTSL'),
);
const ProductDetail = React.lazy(() =>
  _lazyLoad('@/views/ProductManage/ProductDetail'),
);
const DeviceManage = React.lazy(() => _lazyLoad('@/views/DeviceManage'));
const GroupManage = React.lazy(() => _lazyLoad('@/views/GroupManage'));
const ReleasePlanManage = React.lazy(() =>
  _lazyLoad('@/views/ReleasePlanManage'),
);
const CheckConfSoftware = React.lazy(() =>
  _lazyLoad('@/views/ReleasePlanManage/CheckConfSoftware'),
);
const ConfigEditAndAdd = React.lazy(() =>
  _lazyLoad('@/views/ConfigManagement/EditAndAdd'),
);
const VehicleTypeConfig = React.lazy(() =>
  _lazyLoad('@/views/VehicleTypeManage/VehicleTypeConfig'),
);
const VehicleTypeHandleHistory = React.lazy(() =>
  _lazyLoad('@/views/VehicleTypeManage/VehicleTypeHandleHistory'),
);
const EditAndAddVehicle = React.lazy(() =>
  _lazyLoad('@/views/VehicleConfigManage/EditAndAddVehicle'),
);
const ConfRecord = React.lazy(() =>
  _lazyLoad('@/views/VehicleConfigAndRelease/ConfRecord'),
);
const PublishRecord = React.lazy(() =>
  _lazyLoad('@/views/VehicleConfigAndRelease/PublishRecord'),
);
const ReleaseConfSoftware = React.lazy(() =>
  _lazyLoad('@/views/VehicleConfigAndRelease/ReleaseConfSoftware'),
);
const VehicleConf = React.lazy(() =>
  _lazyLoad('@/views/VehicleConfigAndRelease/VehicleConf'),
);
const HardwareSerialManage = React.lazy(() =>
  _lazyLoad('@/views/HardwareSerialManage'),
);
const UpgradePackageManagement = React.lazy(() =>
  _lazyLoad('@/views/UpgradePackageManagement'),
);
const NoPermission = React.lazy(() => _lazyLoad('@/views/NoPermission'));
const CommandControl = React.lazy(() => _lazyLoad('@/views/CommandControl'));
const AddTask = React.lazy(() => _lazyLoad('@/views/CommandControl/AddTask'));
const CheckTask = React.lazy(() =>
  _lazyLoad('@/views/CommandControl/CheckTask'),
);
const Firmware = React.lazy(() => _lazyLoad('@/views/Firmware'));
const FirmwareInfo = React.lazy(() =>
  _lazyLoad('@/views/Firmware/FirmwareInfo'),
);
const AddPackage = React.lazy(() => _lazyLoad('@/views/Firmware/AddPackage'));
const PushDetail = React.lazy(() => _lazyLoad('@/views/Firmware/PushDetail'));
const ReleasePlan = React.lazy(() => _lazyLoad('@/views/ReleasePlan'));
const CreateReleasePlan = React.lazy(() =>
  _lazyLoad('@/views/ReleasePlan/Create'),
);
const ReleasePlanDetail = React.lazy(() =>
  _lazyLoad('@/views/ReleasePlan/Detail'),
);

const routes: any[] = [
  {
    path: '/',
    element: <MainLayout />,
    children: [
      { index: true, element: <Navigate to="configManagement" /> },
      { path: '404', element: <NoPermission /> },
    ],
  },
  {
    path: 'configManagement',
    children: [
      { index: true, element: <ConfigManagement /> },
      { path: 'edit', element: <ConfigEditAndAdd /> },
      { path: 'add', element: <ConfigEditAndAdd /> },
    ],
  },
  {
    path: 'vehicleTypeManage',
    children: [
      { index: true, element: <VehicleTypeManage /> },
      { path: 'vehicleTypeConfig', element: <VehicleTypeConfig /> },
      {
        path: 'vehicleTypeHandleHistory',
        element: <VehicleTypeHandleHistory />,
      },
    ],
  },
  {
    path: 'vehicleConfigManage',
    children: [
      { index: true, element: <VehicleConfigManage /> },
      { path: 'edit', element: <EditAndAddVehicle /> },
      { path: 'add', element: <EditAndAddVehicle /> },
    ],
  },
  {
    path: 'vehicleConfigAndRelease',
    children: [
      { index: true, element: <VehicleConfigAndRelease /> },
      { path: 'confRecord', element: <ConfRecord /> },
      { path: 'publishRecord', element: <PublishRecord /> },
      { path: 'releaseConfSoftware', element: <ReleaseConfSoftware /> },
      { path: 'vehicleConf', element: <VehicleConf /> },
    ],
  },
  {
    path: 'releasePlanManage',
    children: [
      { index: true, element: <ReleasePlanManage /> },
      { path: 'checkConfSoftware', element: <CheckConfSoftware /> },
    ],
  },
  {
    path: 'hardwareSerialManage',
    children: [{ index: true, element: <HardwareSerialManage /> }],
  },
  {
    path: 'upgrademanagement',
    children: [{ index: true, element: <UpgradePackageManagement /> }],
  },
  {
    path: 'device',
    children: [{ index: true, element: <DeviceManage /> }],
  },
  {
    path: 'product',
    children: [
      { index: true, element: <ProductManage /> },
      { path: 'AddProduct', element: <AddProduct /> },
      { path: 'TSLDraft', element: <TSLDraft /> },
      { path: 'TSLModel', element: <TSLModel /> },
      { path: 'EditAddTSL', element: <EditAddTSL /> },
      { path: 'ProductDetail', element: <ProductDetail /> },
    ],
  },
  {
    path: 'group',
    children: [{ index: true, element: <GroupManage /> }],
  },
  {
    path: 'commandControl',
    children: [
      { index: true, element: <CommandControl /> },
      { path: 'AddTask', element: <AddTask /> },
      { path: 'CheckTask', element: <CheckTask /> },
    ],
  },
  {
    path: 'firmware',
    children: [
      { index: true, element: <Firmware pageType="firmware" /> },
      { path: 'firmwareInfo', element: <FirmwareInfo /> },
      {
        path: 'addPackage',
        element: <AddPackage />,
      },
      {
        path: 'pushDetail',
        element: <PushDetail />,
      },
    ],
  },
  {
    path: 'app',
    children: [
      { index: true, element: <Firmware pageType="app" /> },
      { path: 'firmwareInfo', element: <FirmwareInfo /> },
      {
        path: 'addPackage',
        element: <AddPackage />,
      },
      {
        path: 'pushDetail',
        element: <PushDetail />,
      },
    ],
  },
  {
    path: 'releasePlan',
    children: [
      { index: true, element: <ReleasePlan /> },
      { path: 'create', element: <CreateReleasePlan /> },
      { path: 'detail', element: <ReleasePlanDetail /> },
    ],
  },
];

export default routes;
