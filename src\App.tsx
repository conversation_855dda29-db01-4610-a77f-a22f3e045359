import React, { useEffect } from 'react';
import routes from '@/routes';
import { useLocation, useRoutes } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/es/locale/zh_CN';
import '@/assets/css/all.scss';
import {
  addGlobalEventListener,
  removeGlobalEventListener,
} from './utils/emit';
import { useNavigate } from 'react-router-dom';
import { removeSearchValues } from '@/redux/reducers/searchform';
import { useDispatch } from 'react-redux';

const App = () => {
  const routing = useRoutes(routes);
  const navigator = useNavigate();
  const dispatch = useDispatch();

  useEffect(() => {
    const cb = () => {
      navigator('/404');
    };
    addGlobalEventListener('toNoPermission', cb);
    const menuCb = (val: any) => {
      if (val === 'ota') {
        dispatch(removeSearchValues({}));
      }
    };
    (window as any)._QIANKUN_SDK_?.event?.on(
      'clear-microApp-searchInfo',
      menuCb,
    );
    return () => {
      removeGlobalEventListener('toNoPermission', cb);
      (window as any)._QIANKUN_SDK_?.event?.remove(
        'clear-microApp-searchInfo',
        menuCb,
      );
    };
  }, []);
  return (
    <ConfigProvider locale={zhCN}>
      <div
        className="main-container"
        style={{ height: '100%', padding: '10px', backgroundColor: '#f0f2f5' }}
      >
        {routing}
      </div>
    </ConfigProvider>
  );
};

export default App;
