import React from 'react';
import { Modal, message } from 'antd';
import ConfigItemForm from './ConfigItemForm';
import ConfigItemRequest from '@/fetch/bussiness/configItem';
import { HttpStatusCode } from '@/fetch/core/constant';

interface CreateConfigItemProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const CreateConfigItem: React.FC<CreateConfigItemProps> = ({ visible, onClose, onSuccess }) => {
  const configItemRequest = new ConfigItemRequest();

  // 提交表单
  const handleSubmit = async (values: any) => {
    try {
      const res = await configItemRequest.addConfigItem(values);
      
      if (res && (res.code === HttpStatusCode.Success || res.code === 200 || res.code === '200')) {
        message.success('新增配置项成功');
        onSuccess();
        onClose();
      } else {
        message.error(res.message || '新增配置项失败');
      }
    } catch (error) {
      console.error('新增配置项出错:', error);
      message.error('新增配置项出错');
    }
  };

  return (
    <Modal
      title="新建结构化配置项"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      destroyOnClose
    >
      <ConfigItemForm
        onSubmit={handleSubmit}
        onCancel={onClose}
      />
    </Modal>
  );
};

export default CreateConfigItem;
