import React, { useEffect, useRef, useState } from 'react';
import { FormConfig } from '@/components/CommonForm/formConfig';
import {
  CheckTaskInfoFormConfig1,
  CheckTaskInfoFormConfig2,
  PushDetailForm,
  PushDetailTableConfig,
} from '../utils/column';
import CommonForm from '@/components/CommonForm';
import { FirmwareFetch, getTaskDetail } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { formatLocation } from '@/utils/formatLocation';
import { CommonTable, useTableData } from '@jd/x-coreui';
import BreadCrumb from '@/components/BreadCrumb';
import { Col, message, Row, Table } from 'antd';
import UpgradeStatus, { Status } from '../components/UpgradeStatus';
import './index.scss';
import showModal from '@/components/commonModal';
import { saveInfo, removeInfo } from '@/redux/reducers/createOTATask';
import { FieldItem } from '@/components/CommonForm/formConfig';
import { isEmpty, isEqual } from 'lodash';

const PushDetail = () => {
  const fetchApi = new FirmwareFetch();
  const dispatch = useDispatch();
  const navigator = useNavigate();
  const {
    type,
    productKey,
    productName,
    appName,
    appVersionNumber,
    appEnable,
    appAlias,
    versionEnable,
  } = formatLocation(window.location.search);
  const initSearchCondition = useRef<any>({
    searchForm: {
      productKey: { label: productName, value: productKey },
      appVersionNumber: appVersionNumber,
      appType: type,
      appName: appName,
      groupNoList: [],
      online: null,
      issueDeviceStatusList: [],
      deviceName: null,
    },
    pageNum: 1,
    pageSize: 10,
  });
  const [searchCondition, setSearchCondition] = useState<any>(
    initSearchCondition.current,
  );
  const [selectedDevice, setSelectedDevice] = useState<string[]>([]);
  const [selectedDeviceInfo, setSelectedDeviceInfo] = useState<any[]>([]);
  const [basicInfo, setBasicInfo] = useState<any>({});

  const row = new Map([
    ['版本号', 'appVersion'],
    ['版本编号', 'appVersionNumber'],
    [type === 'firmware' ? '固件' : '应用', 'appAlias'],
    ['产品型号', 'productModelNoMap'],
    ['创建时间', 'createTime'],
    ['创建人', 'createUser'],
    ['更新时间', 'modifyTime'],
    ['最后操作人', 'modifyUser'],
  ]);
  const { tableData, loading, reloadTable } = useTableData(
    searchCondition
      ? {
          ...searchCondition?.searchForm,
          pageNum: searchCondition?.pageNum,
          pageSize: searchCondition?.pageSize,
          appType: type,
          productKey,
          appName,
          appVersionNumber,
        }
      : null,
    fetchApi.getPushDetailDevice,
    'pushDetailPage',
    true,
  );
  useEffect(() => {
    getVersionInfo();
  }, []);
  const getVersionInfo = async () => {
    const res = await fetchApi.getAppVersionInfo({
      type,
      productKey,
      appName,
      appVersionNumber,
    });
    if (res.code === HttpStatusCode.Success) {
      setBasicInfo(res.data);
    }
  };
  const formatColumns = () => {
    return PushDetailTableConfig?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${
              (searchCondition.pageNum - 1) * searchCondition.pageSize +
              index +
              1
            }`;
          break;
        case 'issueDeviceStatus':
          col.render = (text: any, record: any, index: number) => {
            return <UpgradeStatus status={record.issueDeviceStatus} />;
          };
          break;
        case 'operate':
          col.render = (item: any, record: any) => {
            return (
              <div className="operate-btn">
                <a
                  onClick={() => {
                    handleCheckTaskInfo(record.issueTaskNumber);
                  }}
                >
                  查看任务参数
                </a>
              </div>
            );
          };
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };
  const handleCheckTaskInfo = async (issueTaskNumber: string) => {
    let formInstance: any = null;
    const _mergeConfig: FormConfig = {
      fields: [
        ...CheckTaskInfoFormConfig1.fields?.map((item: FieldItem) => ({
          ...item,
          disabled: true,
          labelCol: item.labelCol ? item.labelCol : { span: 5 },
          wrapperCol: item.wrapperCol ? item.wrapperCol : { span: 18 },
        })),
        ...CheckTaskInfoFormConfig2.fields?.map((item: FieldItem) => ({
          ...item,
          disabled: true,
          labelCol: item.labelCol ? item.labelCol : { span: 5 },
          wrapperCol: item.wrapperCol ? item.wrapperCol : { span: 18 },
        })),
      ],
      linkRules: {
        ...CheckTaskInfoFormConfig1.linkRules,
        ...CheckTaskInfoFormConfig2.linkRules,
      },
    };
    const res = await getTaskDetail(issueTaskNumber);
    if (res?.code === HttpStatusCode.Success) {
      const { issueAppInfoList, productModelNoMap, ...otherData } =
        res?.data || {};
      const info = {
        ...(res?.data || {}),
        productModelNoMap: Object.values(productModelNoMap ?? {}).join(','),
        ...(!isEmpty(issueAppInfoList) ? issueAppInfoList[0] : {}),
      };
      formInstance?.setFieldsValue(info);
      showModal({
        title: '配置及软件发布',
        width: '1000px',
        content: (
          <CommonForm
            defaultValue={info}
            getFormInstance={(ref: any) => {
              formInstance = ref;
            }}
            formConfig={_mergeConfig}
          />
        ),
        footer: [
          {
            text: '关闭',
            type: 'notCancelBtn',
            needValidate: true,
            onClick: (cb: any) => {
              cb();
            },
          },
        ],
      });
    }
  };
  const onSearchClick = (val: any) => {
    const newValue = {
      searchForm: val,
      pageNum: 1,
      pageSize: 10,
    };
    if (!isEqual(newValue, searchCondition)) {
      setSearchCondition(newValue);
    } else {
      reloadTable();
    }
  };
  const onResetClick = () => {
    setSearchCondition(initSearchCondition.current);
    setSelectedDevice([]);
    setSelectedDeviceInfo([]);
  };

  const rowSelection = {
    selectedRowKeys: selectedDevice,
    onSelect: (record: any, selected: boolean) => {
      if (selected) {
        setSelectedDevice(selectedDevice.concat([record.issueDeviceId]));
        setSelectedDeviceInfo(selectedDeviceInfo.concat([record]));
      } else {
        setSelectedDevice(
          selectedDevice.filter((v) => v !== record.issueDeviceId),
        );
        setSelectedDeviceInfo(
          selectedDeviceInfo.filter(
            (v) => v.issueDeviceId !== record.issueDeviceId,
          ),
        );
      }
    },
    onSelectAll: (selected: boolean, selectedRows: any, changeRows: any) => {
      if (selected) {
        const set1 = new Set(selectedDevice);
        const set2 = new Set(selectedDeviceInfo);
        changeRows.forEach((v: any) => {
          set1.add(v.issueDeviceId);
          set2.add(v);
        });
        setSelectedDevice([...set1]);
        setSelectedDeviceInfo([...set2]);
      } else {
        const arr1 = selectedDeviceInfo.filter((v) => {
          return !changeRows.some(
            (i: any) => i.issueDeviceId === v.issueDeviceId,
          );
        });
        const arr2 = selectedDevice.filter((v) => {
          return !changeRows.some((i: any) => i.issueDeviceId === v);
        });
        setSelectedDevice([...arr2]);
        setSelectedDeviceInfo([...arr1]);
      }
    },
  };

  const countUpgradeStatus = async () => {
    const res = await fetchApi.countUpgradeStatus({
      ...searchCondition?.searchForm,
      issueDeviceStatusList:
        searchCondition?.searchForm?.issueDeviceStatusList?.map(
          (v: any) => v.value,
        ),
      online: searchCondition?.searchForm?.online?.value,
      appType: type,
      productKey,
      appName,
      appVersionNumber,
    });
    if (res.code === HttpStatusCode.Success) {
      const cols: any[] = [];
      const val: any = {};
      res.data.forEach((v: any) => {
        cols.push({
          title: v.name,
          dataIndex: v.key,
        });
        val[v.key] = v.value;
      });
      showModal({
        title: '升级状态统计',
        width: '600px',
        content: (
          <Table
            columns={cols}
            dataSource={[val]}
            pagination={false}
            rowKey={'success'}
          />
        ),
        footer: [
          {
            text: '知道了',
            type: 'notCancelBtn',
            onClick: (cb: any) => {
              cb();
            },
          },
        ],
      });
    }
  };
  const handleResend = () => {
    const deviceNameList = selectedDeviceInfo.map((v) => v.deviceName);
    if (deviceNameList.length <= 0) {
      message.error('请选择设备');
      return;
    }
    const keyArr: string[] = [];
    selectedDeviceInfo.forEach((v: any) => {
      if (
        [Status.to_be_effective, Status.creating, Status.stop].includes(
          v.issueDeviceStatus,
        )
      ) {
        keyArr.push(v.detailNo);
      }
    });
    if (keyArr.length > 0) {
      showModal({
        title: '“创建中/待生效/已终止”的不可重新推送',
        width: '600px',
        content: '',
        footer: [
          {
            text: '知道了',
            type: 'notCancelBtn',
            onClick: (cb: any) => {
              cb();
            },
          },
        ],
      });
      return;
    }
    showModal({
      title: `确定重新推送${deviceNameList.length}台设备的升级？`,
      width: '600px',
      content: '',
      footer: [
        {
          text: '取消',
          type: 'cancelBtn',
          onClick: (cb: any) => {
            cb();
          },
        },
        {
          text: '确定',
          type: 'notCancelBtn',
          needValidate: true,
          onClick: async (cb: any) => {
            dispatch(
              saveInfo({
                appType: basicInfo.type,
                productKey: basicInfo.productKey,
                productModelNoList: Object.keys(
                  basicInfo.productModelNoMap || {},
                ),
                appName: basicInfo.appName,
                appVersionNumber: basicInfo.appVersionNumber,
                deviceNameList,
                currentStep: 2,
              }),
            );
            cb();
            navigator('/releasePlan/create');
          },
        },
      ],
    });
  };
  const handleStop = () => {
    if (selectedDevice.length <= 0) {
      message.error('请选择设备');
      return;
    }
    const keyArr: string[] = [];
    selectedDeviceInfo.forEach((v: any) => {
      if (
        [
          Status.already_effective,
          Status.to_be_effective,
          Status.received,
          Status.downloading,
          Status.download_success,
        ].includes(v.issueDeviceStatus)
      ) {
        keyArr.push(v.detailNo);
      }
    });
    if (keyArr.length !== selectedDevice.length) {
      showModal({
        title: '仅“已生效、待生效、已接收、下载中、下载完成“状态可终止',
        width: '600px',
        content: '',
        footer: [
          {
            text: '知道了',
            type: 'notCancelBtn',
            onClick: (cb: any) => {
              cb();
            },
          },
        ],
      });
      return;
    }
    showModal({
      title: `确定终止${selectedDevice.length}台设备的升级？若设备已经执行升级任务，终止操作不一定成功`,
      width: '600px',
      content: '',
      footer: [
        {
          text: '取消',
          type: 'cancelBtn',
          onClick: (cb: any) => {
            cb();
          },
        },
        {
          text: '确定',
          type: 'notCancelBtn',
          needValidate: true,
          onClick: async (cb: any) => {
            const res = await fetchApi.handleStopDevice({
              appType: type,
              productKey,
              appName,
              appVersionNumber,
              issueDeviceIdList: selectedDevice,
            });
            if (res.code === HttpStatusCode.Success) {
              message.success('下发终止成功');
            } else {
              message.error(res.message);
            }
            cb();
          },
        },
      ],
    });
  };
  const handleCheckDetail = () => {
    const path = type === 'firmware' ? '/firmware' : '/app';
    navigator(
      path +
        '/addPackage?type=' +
        basicInfo.type +
        '&operateType=edit' +
        '&appVersionNumber=' +
        basicInfo.appVersionNumber +
        '&productKey=' +
        basicInfo.productKey +
        '&productName=' +
        productName +
        '&appName=' +
        basicInfo.appName +
        '&appAlias=' +
        basicInfo.appAlias +
        '&appEnable=' +
        basicInfo.enable +
        '&appVersionNumber=' +
        basicInfo.appVersionNumber,
    );
  };
  const middleBtns: any[] =
    appEnable == 1 && versionEnable == 1
      ? [
          {
            show: true,
            title: '新建升级任务',
            key: 'add-task',
            onClick: () => {
              dispatch(
                saveInfo({
                  appType: basicInfo.type,
                  productKey: basicInfo.productKey,
                  productModelNoList: Object.keys(
                    basicInfo.productModelNoMap || {},
                  ),
                  appName: basicInfo.appName,
                  appAlias: basicInfo.appAlias,
                  appVersion: basicInfo.appVersion,
                  appVersionNumber: basicInfo.appVersionNumber,
                  currentStep: 0,
                }),
              );
              navigator('/releasePlan/create');
            },
          },
          {
            show: true,
            title: '升级状态统计',
            key: 'upgrade-status-statistics',
            onClick: () => countUpgradeStatus(),
          },
          {
            show: true,
            title: '重新推送',
            key: 'resend',
            onClick: () => handleResend(),
          },
          {
            show: true,
            title: '终止',
            key: 'stop',
            onClick: () => handleStop(),
          },
        ]
      : [
          {
            show: true,
            title: '升级状态统计',
            key: 'upgrade-status-statistics',
            onClick: () => countUpgradeStatus(),
          },
          {
            show: true,
            title: '终止',
            key: 'stop',
            onClick: () => handleStop(),
          },
        ];

  return (
    <div className="push-detail-page">
      <BreadCrumb
        items={[
          {
            title: '通用设备管理',
            route: type === 'firmware' ? '/firmware' : '/app',
          },
          {
            title: '升级包管理',
            route: type === 'firmware' ? '/firmware' : '/app',
          },
          {
            title: type === 'firmware' ? '固件' : '应用',
            route: type === 'firmware' ? '/firmware' : '/app',
          },
          {
            title: type === 'firmware' ? '固件信息' : '应用信息',
            route:
              `${
                type === 'firmware' ? '/firmware' : '/app'
              }/firmwareInfo?type=` +
              type +
              '&productKey=' +
              productKey +
              '&productName=' +
              productName +
              '&appName=' +
              appName +
              '&appAlias=' +
              appAlias +
              '&appEnable=' +
              appEnable,
          },
          { title: '推送详情', route: '' },
        ]}
      />
      <div className="version-info">
        <div className="title">版本信息</div>

        <Row>
          {Array.from(row.keys()).map((label: string) => {
            const name = row.get(label);
            let v = basicInfo[name!];
            if (name === 'productModelNoMap' && v) {
              v = Object.values(v).join(',');
            }
            return (
              <Col span={6} className="col" key={name}>
                <span> {label}：</span>
                <span>{v || '-'}</span>
              </Col>
            );
          })}
        </Row>
        <a className="detail-btn" onClick={handleCheckDetail}>
          详细信息
        </a>
      </div>
      <CommonForm
        name="firmware-pushDetail-searchForm"
        formConfig={PushDetailForm}
        layout={'inline'}
        defaultValue={initSearchCondition.current.searchForm}
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={onResetClick}
      />
      <CommonTable
        searchCondition={searchCondition}
        loading={loading}
        tableListData={{
          list: tableData?.list || [],
          totalPage: tableData?.pages,
          totalNumber: tableData?.total,
        }}
        rowSelection={{ type: 'checkbox', ...rowSelection }}
        middleBtns={middleBtns}
        columns={formatColumns()}
        rowKey={'issueDeviceId'}
        onPageChange={(paginationData: any) => {
          setSearchCondition({
            ...searchCondition,
            pageNum: paginationData.pageNum,
            pageSize: paginationData.pageSize,
          });
        }}
      ></CommonTable>
    </div>
  );
};

export default React.memo(PushDetail);
