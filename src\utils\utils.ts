export const isNullObject = (obj: any) => {
  if (!obj) {
    return true;
  }
  return Object.keys(obj).length === 0;
};
export const isEmpty = (param: any) => {
  if (Array.isArray(param) || typeof param === 'string') {
    return param.length === 0;
  }
  return true;
};

export const regex = /^[\u4e00-\u9fa5a-zA-Z0-9_]+$/; // 匹配中文、英文字母、数字和下划线（_）

/**
 * 将原始数据格式化为树形或级联选择器所需的数据结构。
 * @param params - 包含原始数据、类型、层级、产品键和父键的参数对象。
 * @returns 格式化后的数据。
 */
export const formatTreeData = (params: {
  origin: any[];
  type: 'Tree' | 'Cascader';
  level: number;
  disabledLevel?: number;
  productKey?: string;
  parentKey?: string[];
}) => {
  const { origin, type, level, productKey, parentKey, disabledLevel } = params;
  const data = productKey
    ? origin.filter((item) => item.productKey === productKey)
    : origin;
  return data.map((item: any) => {
    const parantKey = [...(parentKey ?? []), item.groupNo];
    const obj: any =
      type == 'Tree'
        ? {
            title: item.name,
            key: item.groupNo,
            parentKey: parantKey,
          }
        : {
            label: item.name,
            value: item.groupNo,
          };
    if (type === 'Cascader' && disabledLevel && level >= disabledLevel) {
      obj.disabled = true;
    }
    if (item.children) {
      obj.children = formatTreeData({
        origin: item.children,
        type,
        level: level + 1,
        productKey,
        parentKey: parantKey,
      });
    }
    return obj;
  });
};

export const debounce = (fn: any, wait: number = 50) => {
  let timer: any = null;
  return function (..._args: any) {
    if (timer) {
      clearTimeout(timer);
    }

    timer = setTimeout(() => {
      fn.apply(null, _args);
    }, wait);
  };
};

export const flat = (arr: any[]) => {
  return arr?.reduce((pre, cur) => {
    return pre.concat(Array.isArray(cur) ? flat(cur) : cur);
  }, []);
};
export const NULL_GROUP: {
  label: string;
  value: string;
} = {
  label: '未分组',
  value: 'NULL',
};
