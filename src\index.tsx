import './public-path';
import React from 'react';
import { BrowserRouter } from 'react-router-dom';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { Spin } from 'antd';
import App from './App';
import { store } from '@/redux/store';
import '@/assets/css/all.scss';
let root: any;
const height = window.innerHeight / 2;
function render(props: any) {
  const { container } = props;
  const rootNode = container
    ? container.querySelector('#ota-root')!
    : document.getElementById('ota-root');
  root = ReactDOM.createRoot(rootNode);

  root.render(
    <React.Suspense
      fallback={
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            marginTop: `${height}px`,
          }}
        >
          <Spin />
        </div>
      }
    >
      <Provider store={store}>
        <BrowserRouter
          basename={(window as any).__POWERED_BY_QIANKUN__ ? '/ota' : '/'}
        >
          <App />
        </BrowserRouter>
      </Provider>
    </React.Suspense>,
  );
}
/**
 * bootstrap 只会在微应用初始化的时候调用一次，下次微应用重新进入时会直接调用 mount 钩子，不会再重复触发 bootstrap。
 * 通常我们可以在这里做一些全局变量的初始化，比如不会在 unmount 阶段被销毁的应用级别的缓存等。
 */
export async function bootstrap() {
  console.log('%c%s', 'color: red;', '子应用bootstraped');
}

/**
 * 应用每次进入都会调用 mount 方法，通常我们在这里触发应用的渲染方法
 */
export async function mount(props: any) {
  console.log('%c%s', 'color: red;', '子应用mount', props);
  (window as any).actions = props;
  render(props);
}

/**
 * 应用每次 切出/卸载 会调用的方法，通常在这里我们会卸载微应用的应用实例
 */
export async function unmount(props: any) {
  console.log('%c%s', 'color: red;', '子应用unmount');
  // const { container } = props;
  // const rootNode = container.querySelector('#ota-root')!;
  // root = ReactDOM.createRoot(rootNode);
  // 销毁 React 根节点中的一个已经渲染的树
  root.unmount();
}

if (!(window as any).__POWERED_BY_QIANKUN__) {
  const rootNode = document.getElementById('ota-root')!;
  root = ReactDOM.createRoot(rootNode);
  root.render(
    <React.Suspense
      fallback={
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            marginTop: `${height}px`,
          }}
        >
          <Spin />
        </div>
      }
    >
      <Provider store={store}>
        <BrowserRouter>
          <App />
        </BrowserRouter>
      </Provider>
    </React.Suspense>,
  );
}
