import React, { useState } from 'react';
import { Form, Input, Select, Button, Row, Col, Divider, Space, Tooltip, InputNumber, Switch } from 'antd';
import { PlusOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import './ParamEditor.scss';

// 数据类型枚举
export enum DataType {
  STRING = 'STRING',
  INTEGER = 'INTEGER',
  FLOAT = 'FLOAT',
  BOOLEAN = 'BOOLEAN',
  ENUM = 'ENUM',
  DATE = 'DATE',
  OBJECT = 'OBJECT',
  ARRAY = 'ARRAY',
}

// 数据类型选项
export const dataTypeOptions = [
  { label: '字符串', value: DataType.STRING },
  { label: '整数', value: DataType.INTEGER },
  { label: '浮点数', value: DataType.FLOAT },
  { label: '布尔值', value: DataType.BOOLEAN },
  { label: '枚举', value: DataType.ENUM },
  { label: '日期', value: DataType.DATE },
  { label: '对象', value: DataType.OBJECT },
  { label: '数组', value: DataType.ARRAY },
];

// 参数项接口
export interface ParamItem {
  id: string;
  paramName: string;
  paramKey: string;
  dataType: DataType;
  description?: string;
  defaultValue?: any;
  enumValues?: string[];
  required?: boolean;
  min?: number;
  max?: number;
  precision?: number;
  format?: string;
  items?: ParamItem; // 用于数组类型
  properties?: ParamItem[]; // 用于对象类型
}

interface ParamEditorProps {
  params: ParamItem[];
  onChange: (params: ParamItem[]) => void;
}

const ParamEditor: React.FC<ParamEditorProps> = ({ params, onChange }) => {
  // 添加参数
  const addParam = () => {
    const newParam: ParamItem = {
      id: Date.now().toString(),
      paramName: '',
      paramKey: '',
      dataType: DataType.STRING,
      required: true,
    };
    onChange([...params, newParam]);
  };

  // 删除参数
  const removeParam = (id: string) => {
    onChange(params.filter(param => param.id !== id));
  };

  // 查看参数详情
  const viewParam = (param: ParamItem) => {
    console.log('参数详情:', param);
  };

  // 更新参数
  const updateParam = (id: string, field: string, value: any) => {
    onChange(
      params.map(param => {
        if (param.id === id) {
          // 如果更改了数据类型，重置特定类型的字段
          if (field === 'dataType') {
            const updatedParam = { ...param, [field]: value };
            // 根据新的数据类型清除不相关的字段
            switch (value) {
              case DataType.STRING:
                delete updatedParam.min;
                delete updatedParam.max;
                delete updatedParam.precision;
                delete updatedParam.enumValues;
                delete updatedParam.format;
                delete updatedParam.items;
                delete updatedParam.properties;
                break;
              case DataType.INTEGER:
              case DataType.FLOAT:
                delete updatedParam.enumValues;
                delete updatedParam.format;
                delete updatedParam.items;
                delete updatedParam.properties;
                break;
              case DataType.BOOLEAN:
                delete updatedParam.min;
                delete updatedParam.max;
                delete updatedParam.precision;
                delete updatedParam.enumValues;
                delete updatedParam.format;
                delete updatedParam.items;
                delete updatedParam.properties;
                break;
              case DataType.ENUM:
                delete updatedParam.min;
                delete updatedParam.max;
                delete updatedParam.precision;
                delete updatedParam.format;
                delete updatedParam.items;
                delete updatedParam.properties;
                break;
              case DataType.DATE:
                delete updatedParam.min;
                delete updatedParam.max;
                delete updatedParam.precision;
                delete updatedParam.enumValues;
                delete updatedParam.items;
                delete updatedParam.properties;
                break;
              case DataType.OBJECT:
                delete updatedParam.min;
                delete updatedParam.max;
                delete updatedParam.precision;
                delete updatedParam.enumValues;
                delete updatedParam.format;
                delete updatedParam.items;
                if (!updatedParam.properties) {
                  updatedParam.properties = [];
                }
                break;
              case DataType.ARRAY:
                delete updatedParam.min;
                delete updatedParam.max;
                delete updatedParam.precision;
                delete updatedParam.enumValues;
                delete updatedParam.format;
                delete updatedParam.properties;
                if (!updatedParam.items) {
                  updatedParam.items = {
                    id: `${id}_items`,
                    paramName: `${param.paramName}项`,
                    paramKey: `${param.paramKey}Item`,
                    dataType: DataType.STRING,
                  };
                }
                break;
            }
            return updatedParam;
          }
          return { ...param, [field]: value };
        }
        return param;
      })
    );
  };

  // 渲染不同数据类型的参数输入控件
  const renderParamFields = (param: ParamItem) => {
    switch (param.dataType) {
      case DataType.STRING:
        return (
          <div className="param-type-fields">
            <Form.Item label="默认值">
              <Input 
                placeholder="请输入默认值" 
                value={param.defaultValue} 
                onChange={e => updateParam(param.id, 'defaultValue', e.target.value)} 
              />
            </Form.Item>
          </div>
        );
      case DataType.INTEGER:
      case DataType.FLOAT:
        return (
          <div className="param-type-fields">
            <Row gutter={24}>
              <Col span={8}>
                <Form.Item label="最小值">
                  <InputNumber 
                    placeholder="请输入最小值" 
                    value={param.min} 
                    onChange={value => updateParam(param.id, 'min', value)} 
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="最大值">
                  <InputNumber 
                    placeholder="请输入最大值" 
                    value={param.max} 
                    onChange={value => updateParam(param.id, 'max', value)} 
                  />
                </Form.Item>
              </Col>
              {param.dataType === DataType.FLOAT && (
                <Col span={8}>
                  <Form.Item label="精度">
                    <InputNumber 
                      placeholder="请输入精度" 
                      value={param.precision} 
                      onChange={value => updateParam(param.id, 'precision', value)} 
                    />
                  </Form.Item>
                </Col>
              )}
            </Row>
            <Form.Item label="默认值">
              <InputNumber 
                placeholder="请输入默认值" 
                value={param.defaultValue} 
                onChange={value => updateParam(param.id, 'defaultValue', value)} 
              />
            </Form.Item>
          </div>
        );
      case DataType.BOOLEAN:
        return (
          <div className="param-type-fields">
            <Form.Item label="默认值">
              <Select
                placeholder="请选择默认值"
                value={param.defaultValue}
                onChange={value => updateParam(param.id, 'defaultValue', value)}
                options={[
                  { label: '是', value: true },
                  { label: '否', value: false }
                ]}
              />
            </Form.Item>
          </div>
        );
      case DataType.ENUM:
        return (
          <div className="param-type-fields">
            <Form.Item label="枚举值">
              <Input 
                placeholder="请输入枚举值，用逗号分隔" 
                value={param.enumValues?.join(',')} 
                onChange={e => updateParam(param.id, 'enumValues', e.target.value.split(','))} 
              />
            </Form.Item>
            <Form.Item label="默认值">
              <Select
                placeholder="请选择默认值"
                value={param.defaultValue}
                onChange={value => updateParam(param.id, 'defaultValue', value)}
                options={param.enumValues?.map(v => ({ label: v, value: v })) || []}
              />
            </Form.Item>
          </div>
        );
      case DataType.DATE:
        return (
          <div className="param-type-fields">
            <Form.Item label="日期格式">
              <Select
                placeholder="请选择日期格式"
                value={param.format}
                onChange={value => updateParam(param.id, 'format', value)}
                options={[
                  { label: 'YYYY-MM-DD', value: 'YYYY-MM-DD' },
                  { label: 'YYYY-MM-DD HH:mm:ss', value: 'YYYY-MM-DD HH:mm:ss' },
                  { label: 'HH:mm:ss', value: 'HH:mm:ss' }
                ]}
              />
            </Form.Item>
          </div>
        );
      case DataType.OBJECT:
        return (
          <div className="param-type-fields">
            <div className="object-properties">
              <div className="object-properties-header">
                <h4>对象属性</h4>
                <Button 
                  type="link" 
                  icon={<PlusOutlined />} 
                  onClick={() => {
                    const newProperty: ParamItem = {
                      id: `${param.id}_prop_${Date.now()}`,
                      paramName: '',
                      paramKey: '',
                      dataType: DataType.STRING,
                    };
                    const properties = [...(param.properties || []), newProperty];
                    updateParam(param.id, 'properties', properties);
                  }}
                >
                  添加属性
                </Button>
              </div>
              {(param.properties || []).length === 0 ? (
                <div className="no-properties">请添加对象属性</div>
              ) : (
                (param.properties || []).map((property, index) => (
                  <div key={property.id} className="property-item">
                    <Row gutter={24}>
                      <Col span={8}>
                        <Form.Item label="属性名称" required>
                          <Input
                            placeholder="请输入属性名称"
                            value={property.paramName}
                            onChange={e => {
                              const updatedProperties = [...(param.properties || [])];
                              updatedProperties[index] = {
                                ...property,
                                paramName: e.target.value,
                              };
                              updateParam(param.id, 'properties', updatedProperties);
                            }}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item label="属性标识符" required>
                          <Input
                            placeholder="请输入属性标识符"
                            value={property.paramKey}
                            onChange={e => {
                              const updatedProperties = [...(param.properties || [])];
                              updatedProperties[index] = {
                                ...property,
                                paramKey: e.target.value,
                              };
                              updateParam(param.id, 'properties', updatedProperties);
                            }}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item label="数据类型" required>
                          <Select
                            placeholder="请选择数据类型"
                            value={property.dataType}
                            onChange={value => {
                              const updatedProperties = [...(param.properties || [])];
                              updatedProperties[index] = {
                                ...property,
                                dataType: value,
                              };
                              updateParam(param.id, 'properties', updatedProperties);
                            }}
                            options={dataTypeOptions}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                    <div className="property-actions">
                      <Button 
                        type="text" 
                        danger 
                        icon={<DeleteOutlined />} 
                        onClick={() => {
                          const updatedProperties = (param.properties || []).filter(
                            p => p.id !== property.id
                          );
                          updateParam(param.id, 'properties', updatedProperties);
                        }}
                      >
                        删除
                      </Button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        );
      case DataType.ARRAY:
        return (
          <div className="param-type-fields">
            <div className="array-item">
              <h4>数组项类型</h4>
              {param.items && (
                <Row gutter={24}>
                  <Col span={8}>
                    <Form.Item label="项名称" required>
                      <Input
                        placeholder="请输入项名称"
                        value={param.items.paramName}
                        onChange={e => {
                          const updatedItems = {
                            ...param.items,
                            paramName: e.target.value,
                          };
                          updateParam(param.id, 'items', updatedItems);
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="项标识符" required>
                      <Input
                        placeholder="请输入项标识符"
                        value={param.items.paramKey}
                        onChange={e => {
                          const updatedItems = {
                            ...param.items,
                            paramKey: e.target.value,
                          };
                          updateParam(param.id, 'items', updatedItems);
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="数据类型" required>
                      <Select
                        placeholder="请选择数据类型"
                        value={param.items.dataType}
                        onChange={value => {
                          const updatedItems = {
                            ...param.items,
                            dataType: value,
                          };
                          updateParam(param.id, 'items', updatedItems);
                        }}
                        options={dataTypeOptions}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              )}
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="param-editor">
      <div className="param-header">
        <h3>输入参数</h3>
        <Button type="primary" icon={<PlusOutlined />} onClick={addParam}>
          新增参数
        </Button>
      </div>

      {params.length === 0 ? (
        <div className="no-params">请点击"新增参数"按钮添加参数</div>
      ) : (
        params.map((param, index) => (
          <div key={param.id} className="param-item">
            <Divider orientation="left">参数 {index + 1}</Divider>
            <Row gutter={24}>
              <Col span={8}>
                <Form.Item
                  label="参数名称"
                  required
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                >
                  <Input
                    placeholder="请输入参数名称"
                    value={param.paramName}
                    onChange={e => updateParam(param.id, 'paramName', e.target.value)}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="标识符"
                  required
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                >
                  <Input
                    placeholder="请输入标识符"
                    value={param.paramKey}
                    onChange={e => updateParam(param.id, 'paramKey', e.target.value)}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="数据类型"
                  required
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                >
                  <Select
                    placeholder="请选择数据类型"
                    value={param.dataType}
                    onChange={value => updateParam(param.id, 'dataType', value)}
                    options={dataTypeOptions}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={16}>
                <Form.Item
                  label="参数描述"
                  labelCol={{ span: 4 }}
                  wrapperCol={{ span: 20 }}
                >
                  <Input.TextArea
                    placeholder="请输入"
                    rows={2}
                    maxLength={100}
                    showCount
                    value={param.description}
                    onChange={e => updateParam(param.id, 'description', e.target.value)}
                  />
                </Form.Item>
              </Col>
              <Col span={8} className="param-actions">
                <Form.Item label="必填" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                  <Switch
                    checked={param.required}
                    onChange={checked => updateParam(param.id, 'required', checked)}
                  />
                </Form.Item>
                <Space>
                  <Tooltip title="查看详情">
                    <Button 
                      type="text" 
                      icon={<EyeOutlined />} 
                      onClick={() => viewParam(param)}
                    />
                  </Tooltip>
                  <Tooltip title="删除参数">
                    <Button 
                      type="text" 
                      danger 
                      icon={<DeleteOutlined />} 
                      onClick={() => removeParam(param.id)}
                    />
                  </Tooltip>
                </Space>
              </Col>
            </Row>
            {renderParamFields(param)}
          </div>
        ))
      )}
    </div>
  );
};

export default ParamEditor;
