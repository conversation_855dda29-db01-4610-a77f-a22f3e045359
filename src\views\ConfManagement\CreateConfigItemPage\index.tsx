import React from 'react';
import { message } from 'antd';
import { useNavigate } from 'react-router-dom';
import BreadCrumb from '@/components/BreadCrumb';
import ConfigItemForm from '../components/ConfigItemForm';
import ConfigItemRequest from '@/fetch/bussiness/configItem';
import { HttpStatusCode } from '@/fetch/core/constant';
import './index.scss';

// 面包屑导航项
const breadCrumbItems = [
  { title: '通用设备管理', route: '/confManagement' },
  { title: '配置项管理', route: '/confManagement' },
  { title: '结构化配置项', route: '/confManagement' },
  { title: '新建结构化配置项', route: '' },
];

const CreateConfigItemPage: React.FC = () => {
  const navigate = useNavigate();
  const configItemRequest = new ConfigItemRequest();

  // 提交表单
  const handleSubmit = async (values: any) => {
    try {
      const res = await configItemRequest.addConfigItem(values);
      
      if (res && (res.code === HttpStatusCode.Success || res.code === 200 || res.code === '200')) {
        message.success('新增配置项成功');
        // 跳转回列表页面
        navigate('/confManagement');
      } else {
        message.error(res.message || '新增配置项失败');
      }
    } catch (error) {
      console.error('新增配置项出错:', error);
      message.error('新增配置项出错');
    }
  };

  // 取消操作
  const handleCancel = () => {
    navigate('/confManagement');
  };

  return (
    <div className="create-config-item-page">
      <BreadCrumb items={breadCrumbItems} />
      
      <div className="page-content">
        <div className="page-header">
          <h2>新建结构化配置项</h2>
        </div>
        
        <div className="form-container">
          <ConfigItemForm
            onSubmit={handleSubmit}
            onCancel={handleCancel}
          />
        </div>
      </div>
    </div>
  );
};

export default CreateConfigItemPage;
