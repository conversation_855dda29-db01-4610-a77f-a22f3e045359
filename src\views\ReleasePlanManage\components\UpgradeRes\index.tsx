import React, { useState, useEffect, useRef } from 'react';
import { Modal, Form, Table, message } from 'antd';
import { CustomButton, ButtonType } from '@/components/CustomButton';
import { TableListType, pageSizeOptions, SearchCondition } from '@/utils/constant';
import Searchform from '@/components/Searchform';
import {
  UpgradeResSearchConfig,
  UpgradeResTableColumns,
  CheckModuleUpgradeRes,
} from '../../utils/columns';
import { api } from '@/fetch/core/api';
import { request } from '@/fetch/core';
import { UpgradeStatus } from '../../utils/constant';
import { ReleasePlanManageFetch } from '../../utils/fetch';
import { HttpStatusCode } from '@/fetch/core/constant';
import showModal from '@/components/commonModal';

interface Props {
  modalShow: boolean;
  onCancel: Function;
  issueTaskNumber: any;
}
const UpgradeRes = (props: Props) => {
  const fetchApi = new ReleasePlanManageFetch();
  const { modalShow, onCancel, issueTaskNumber } = props;
  const [formRef] = Form.useForm();
  const [searchCondition, setSearchCondition] = useState<SearchCondition>({
    searchForm: {
      city: null,
      station: null,
      vehicleName: null,
      ownerUseCase: [],
      latestIssueTaskResult: null,
    },
    current: 1,
    pageSize: 10,
  });
  const [tableList, setTableList] = useState<TableListType>({
    list: [],
    totalPage: 0,
    totalNumber: 0,
  });
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    fetchTableData(searchCondition);
  }, []);

  /**
   * 获取列表数据
   * @param {Object} searchValues 搜索条件
   */
  const fetchTableData = (searchValues: any) => {
    setLoading(true);
    try {
      request({
        method: 'POST',
        path: api.getIssueTaskResultList,
        body: {
          issueTaskNumber: issueTaskNumber,
          cityId: searchValues.searchForm.city?.value,
          stationId: searchValues.searchForm.station?.value,
          vehicleName: searchValues.searchForm.vehicleName,
          ownerUseCaseList: searchValues.searchForm.ownerUseCase?.map((item: any) => {
            return item.value;
          }),
          status: searchValues.searchForm.latestIssueTaskResult?.value,
          pageNum: searchValues.current,
          pageSize: searchValues.pageSize
        }
      }).then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setTableList({
            list: res.data.list,
            totalPage: res.data.pages,
            totalNumber: res.data.total,
          });
        }
      });
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  };

  const formatColumns = () => {
    return UpgradeResTableColumns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${(searchCondition.current - 1) * searchCondition.pageSize + index + 1}`;
          break;
        case 'statusName':
          col.render = (text: any, record: any, index: number) => {
            return record.status === UpgradeStatus.NOTUPGRADE ? (
              <p style={{ color: 'red', marginBottom: '0px' }}>{record.statusName}</p>
            ) : (
              <p style={{ marginBottom: '0px' }}>{record.statusName}</p>
            );
          };
          break;
        case 'moduleUpgradeRes':
          col.render = (text: any, record: any, index: number) => {
            return (
              <a
                onClick={() => {
                  checkModuleUpgradeRes(record);
                }}
              >
                查看
              </a>
            );
          };
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };

  const formatCheckModuleUpgradeResCol = () => {
    return CheckModuleUpgradeRes?.map((col: any) => {
      switch (col.dataIndex) {
        case 'statuaName':
          col.render = (text: any, record: any, index: number) => {
            return record.status === UpgradeStatus.NOTUPGRADE ? (
              <p style={{ color: 'red', marginBottom: '0px' }}>{record.statusName}</p>
            ) : (
              <p style={{ marginBottom: '0px' }}>{record.statusName}</p>
            );
          };
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };

  const checkModuleUpgradeRes = async (record: any) => {
    const res: any = await fetchApi.fetchModuleUpgradeRes({
      vehicleName: record.vehicleName,
      issueTaskNumber: issueTaskNumber,
    });
    if (res.code === HttpStatusCode.Success && res.data) {
      showModal({
        title: '查看模块升级结果',
        type: 'confirm',
        width: '650',
        content: (
          <Table
            columns={formatCheckModuleUpgradeResCol()}
            dataSource={res.data}
            bordered
            pagination={false}
          />
        ),
        footer: [
          {
            type: 'cancelBtn',
            text: '关闭',
          },
        ],
      });
    } else {
      message.error(res.message);
    }
  };

  const onSearchClick = () => {
    const data = {
      searchForm: formRef.getFieldsValue(),
      pageSize: 10,
      current: 1,
    };
    setSearchCondition(data);
    fetchTableData(data);
  };

  return (
    <Modal
      width={'1200px'}
      title={`${issueTaskNumber}升级结果`}
      onCancel={() => {
        onCancel();
      }}
      visible={modalShow}
      footer={
        <div>
          <CustomButton
            buttonType={ButtonType.DefaultButton}
            onSubmitClick={() => {
              onCancel();
            }}
            title={'退出'}
          />
        </div>
      }
    >
      <Searchform
        configData={UpgradeResSearchConfig}
        onSearchClick={onSearchClick}
        initValues={searchCondition.searchForm}
        formRef={formRef}
        noResetBtn={true}
      />
      <Table
        rowKey={(record) => record.vehicleName}
        loading={loading}
        bordered
        dataSource={tableList.list}
        columns={formatColumns()}
        scroll={{
          y: 500,
        }}
        pagination={{
          position: ['bottomCenter'],
          total: tableList.totalNumber,
          current: searchCondition.current,
          pageSize: searchCondition.pageSize,
          showQuickJumper: true,
          showSizeChanger: true,
          pageSizeOptions: pageSizeOptions,
          showTotal: (total) => `共 ${tableList.totalPage}页,${total} 条记录`,
        }}
        onChange={(paginationData: any, filters: any, sorter: any, extra: any) => {
          if (extra.action === 'paginate') {
            const { current, pageSize } = paginationData;
            const newSearchValue = {
              ...searchCondition,
              current,
              pageSize,
            };
            setSearchCondition(newSearchValue);
            fetchTableData(newSearchValue);
          }
        }}
      />
    </Modal>
  );
};

export default React.memo(UpgradeRes);
