import React, { useState, useRef, useEffect } from 'react';
import { Form, Input, Select, Button, message, Row, Col, Card } from 'antd';
import Device from '@/fetch/bussiness/device';
import TSLModel from '@/fetch/bussiness/TSLModel';
import { HttpStatusCode } from '@/fetch/core/constant';
import ParamEditor, { ParamItem } from './ParamEditor';
import './ConfigItemForm.scss';

// 配置项表单接口
interface ConfigItemFormProps {
  onSubmit: (values: any) => void;
  onCancel: () => void;
  initialValues?: any;
}

const ConfigItemForm: React.FC<ConfigItemFormProps> = ({ onSubmit, onCancel, initialValues }) => {
  const [form] = Form.useForm();
  const [params, setParams] = useState<ParamItem[]>(initialValues?.params || []);
  const [productOptions, setProductOptions] = useState<any[]>([]);
  const [blockOptions, setBlockOptions] = useState<any[]>([]);
  const [configTypeOptions, setConfigTypeOptions] = useState<any[]>([
    { label: '系统配置', value: 'SYSTEM' },
    { label: '业务配置', value: 'BUSINESS' },
    { label: '用户配置', value: 'USER' },
  ]);
  const [selectedProductKey, setSelectedProductKey] = useState<string>('');

  // 获取产品列表
  useEffect(() => {
    fetchProductList();
  }, []);

  // 当产品选择变化时获取模块列表
  useEffect(() => {
    if (selectedProductKey) {
      fetchBlockList(selectedProductKey);
    } else {
      setBlockOptions([]);
    }
  }, [selectedProductKey]);

  // 获取产品列表数据
  const fetchProductList = async () => {
    try {
      const deviceInstance = new Device();
      const res = await deviceInstance.queryProductList();

      if (res && res.code === HttpStatusCode.Success && res.data) {
        const options = res.data.map((item: any) => ({
          label: item.productName,
          value: item.productKey,
        }));
        setProductOptions(options);

        // 如果有产品数据，默认选择第一个
        if (options.length > 0 && !selectedProductKey) {
          const firstProductKey = options[0].value;
          setSelectedProductKey(firstProductKey);
          form.setFieldsValue({ productKey: firstProductKey });
        }
      } else {
        message.error(res.message || '获取产品列表失败');
      }
    } catch (error) {
      message.error('获取产品列表出错');
    }
  };

  // 获取模块列表数据
  const fetchBlockList = async (productKey: string) => {
    if (!productKey) {
      setBlockOptions([]);
      return;
    }

    try {
      const tslModelInstance = new TSLModel();
      const res = await tslModelInstance.getThingModelBlocks({
        productKey,
      });

      if (res && res.code === HttpStatusCode.Success && res.data) {
        const options = res.data.map((item: any) => ({
          label: item.blockName,
          value: item.blockNo,
        }));
        setBlockOptions(options);
      } else {
        message.error(res.message || '获取模块列表失败');
        setBlockOptions([]);
      }
    } catch (error) {
      console.error('获取模块列表出错:', error);
      message.error('获取模块列表出错');
      setBlockOptions([]);
    }
  };

  // 处理参数变化
  const handleParamsChange = (newParams: ParamItem[]) => {
    setParams(newParams);
  };

  // 表单提交
  const handleSubmit = () => {
    form.validateFields()
      .then(values => {
        // 合并表单值和参数列表
        const formData = {
          ...values,
          params: params.map(param => {
            // 根据数据类型处理参数值
            const { id, ...rest } = param;
            return rest;
          })
        };
        onSubmit(formData);
      })
      .catch(errorInfo => {
        console.log('表单验证失败:', errorInfo);
      });
  };

  return (
    <div className="config-item-form">
      <Form
        form={form}
        layout="horizontal"
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        initialValues={initialValues}
      >
        <Card title="基本信息" bordered={false}>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="productKey"
                label="产品模块"
                rules={[{ required: true, message: '请选择产品' }]}
              >
                <Select
                  placeholder="请选择"
                  options={productOptions}
                  onChange={(value) => {
                    setSelectedProductKey(value);
                    form.setFieldsValue({ blockNo: undefined });
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="blockNo"
                label=" "
                colon={false}
              >
                <Select
                  placeholder="请选择"
                  options={blockOptions}
                  disabled={!selectedProductKey}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="itemName"
                label="配置项名称"
                rules={[{ required: true, message: '请输入配置项名称' }]}
              >
                <Input placeholder="请输入" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="itemNo"
                label="标识符"
                rules={[{ required: true, message: '请输入标识符' }]}
              >
                <Input placeholder="请输入" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="configType"
                label="配置类"
                rules={[{ required: true, message: '请选择配置类' }]}
              >
                <Select placeholder="请选择" options={configTypeOptions} />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="description"
            label="描述"
            labelCol={{ span: 3 }}
            wrapperCol={{ span: 21 }}
          >
            <Input.TextArea
              placeholder="请输入"
              rows={4}
              maxLength={100}
              showCount
            />
          </Form.Item>
        </Card>

        <Card title="参数配置" bordered={false} className="param-card">
          <ParamEditor params={params} onChange={handleParamsChange} />
        </Card>

        <div className="form-actions">
          <Button onClick={onCancel}>取消</Button>
          <Button type="primary" onClick={handleSubmit}>
            确定
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default ConfigItemForm;
