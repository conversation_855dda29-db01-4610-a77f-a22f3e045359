import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Radio, Select, message } from 'antd';
import { CustomButton, ButtonType } from '@/components/CustomButton';
import { api } from '@/fetch/core/api';
import { HttpStatusCode } from '@/fetch/core/constant';
import { request } from '@/fetch/core';
interface Props {
  visible: boolean;
  onCancelSaveTemplate: Function;
  onConfirmSaveTemplate: Function;
}
const SaveTemplate = (props: Props) => {
  const { visible, onCancelSaveTemplate, onConfirmSaveTemplate } = props;
  const [form] = Form.useForm();
  const [vehicleTypeOptions, setVehicleTypeOptions] = useState<any[]>();
  const [canOverwriteMsg, setCanOverwriteMsg] = useState<string>(
    '使用该模板的车辆，更改配置字段和值，模板也随之更改'
  );
  useEffect(() => {
    fetchVehicleTypeOptions();
  }, []);
  const fetchVehicleTypeOptions = () => {
    request({
      method: 'POST',
      path: api.getVehicleTypeList,
      body: {
      },
    })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setVehicleTypeOptions(res.data);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const makeOptions = () => {
    let list: any[] = [];
    if (vehicleTypeOptions && vehicleTypeOptions.length > 0) {
      list = vehicleTypeOptions?.map((item: any) => {
        return {
          label: item.vehicleTypeName,
          value: item.vehicleTypeId,
        };
      });
    }
    return list;
  };
  // 确定另存为模板
  const onConfirm = async () => {
    form
      .validateFields()
      .then((values) => {
        onConfirmSaveTemplate(values);
      })
      .catch((errorInfo) => {
        message.error('必填项未填全，操作失败！');
      });
  };
  return (
    <>
      <Modal
        title={'另存为模板'}
        width={'900px'}
        visible={visible}
        onCancel={() => onCancelSaveTemplate()}
        footer={
          <div>
            <CustomButton onSubmitClick={() => onConfirm()} title={'确定'} />
            <CustomButton
              buttonType={ButtonType.DefaultButton}
              onSubmitClick={() => onCancelSaveTemplate()}
              title={'取消'}
            />
          </div>
        }
      >
        <Form
          labelAlign={'right'}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
          form={form}
        >
          <Form.Item
            name={'name'}
            label={'车辆配置模板名称'}
            rules={[{ required: true, message: '请输入车辆配置模板名称' }]}
          >
            <Input placeholder={'请输入车辆配置模板名称'} maxLength={50} />
          </Form.Item>
          <Form.Item
            label="被引用的模板属性"
            name="canOverwrite"
            initialValue={1}
            extra={canOverwriteMsg}
            rules={[{ required: true, message: '请选择被引用的模板属性' }]}
          >
            <Radio.Group
              onChange={(e: any) => {
                setCanOverwriteMsg(
                  e.target.value === 1
                    ? '使用该模板的车辆，更改配置字段和值，模板也随之更改'
                    : ''
                );
              }}
            >
              <Radio value={0}>配置内容不可以覆盖</Radio>
              <Radio value={1}>配置内容可以覆盖</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            extra="注意：仅能加载“已配置”的车型配置的车型，如查询不到，请先配置车型"
            label="所属车型名称"
            name="vehicleTypeId"
            rules={[{ required: true, message: '请选择所属车型名称' }]}
          >
            <Select
              options={makeOptions()}
              placeholder={'请输入车型名称，支持关键字联想全称'}
              filterOption={(input, option) => {
                const label: any = option?.label || '';
                return label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              }}
              allowClear
              showSearch
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default React.memo(SaveTemplate);
