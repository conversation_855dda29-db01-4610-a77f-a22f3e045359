import React, { useEffect, useRef, useState } from 'react';
import {
  Button,
  Form,
  Input,
  Modal,
  Radio,
  Select,
  Table,
  message,
} from 'antd';
import { AppFormData, AppListColumn, ProducTableData } from './columns';
import { request } from '@/fetch/core';
import { HttpStatusCode } from '@/fetch/core/constant';
import _ from 'lodash';
import { ProductType, ProductTypeName } from '@/utils/constant';
import CommonForm from '@/components/CommonForm';
import { FieldItem } from '@/components/CommonForm/formConfig';
const dispathTypeList = [
  {
    label: '配送车',
    value: 'DISPATCH',
  },
  {
    label: '售卖车',
    value: 'SELL',
  },
];

const vendingTypeList = [
  {
    label: '京麟',
    value: 'JING_LIN',
  },
  {
    label: '重德',
    value: 'ZHONG_DE',
  },
];
const AppTable = () => {
  const [form] = Form.useForm();
  const formInstance = useRef<any>(null);
  const [searchConfig, setSearchConfig] = useState<any>(AppFormData);
  const [loading, setLoading] = useState<boolean>(false);
  const [tableList, setTableList] = useState<any[]>([]);
  const [visible, setVisible] = useState<boolean>(false);
  const [defaultData, setDefaultData] = useState<any>(null);
  const [operateType, setOperateType] = useState<any>('');
  const [appList, setAppList] = useState<any[]>([]);
  const [isPublish, setIsPublish] = useState<boolean>(false);
  const [businessTypeList, setBusinessTypeList] =
    useState<{ label: string; value: string }[]>(dispathTypeList);
  const [currentPage, setCurrentPage] = useState<{
    current: number;
    pageSize: number;
  }>({
    current: 1,
    pageSize: 10,
  });
  const [pagination, setPagination] = useState<{
    total: number;
    totalPage: number;
  }>({
    total: 0,
    totalPage: 0,
  });
  useEffect(() => {
    fetchTable(currentPage);
  }, [currentPage]);

  const onDisabled = (record: any) => {
    Modal.confirm({
      content: (
        <p style={{ wordBreak: 'break-all' }}>
          {record.enable
            ? '确定要禁用此包吗，禁用后此包不能创建升级任务'
            : '确定要启用此包吗'}
        </p>
      ),
      onCancel: () => {},
      onOk() {
        request({
          method: 'POST',
          path: '/ota/web/change_product_package_status',
          body: {
            productPackageNumber: record.productPackageNumber,
            enable: record.enable == 0 ? 1 : 0,
          },
        })
          .then((res: any) => {
            fetchTable(currentPage);
          })
          .catch((err) => {
            console.log(err);
          });
      },
    });
  };

  const formatColumns = (columns: any[]) => {
    return columns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'operate':
          col.render = (text: any, record: any) => {
            return (
              <div className="btn-group">
                <a
                  style={{
                    color: record.enable ? '#1677ff' : 'gray',
                  }}
                  onClick={() => {
                    if (record?.enable === 0) {
                      return;
                    }
                    setVisible(true);
                    setOperateType('edit');
                    getProductPackageDetailInfo(record.productPackageNumber)
                      .then((res: any) => {
                        if (res.code === HttpStatusCode.Success) {
                          form.setFieldsValue(record);
                          setBusinessTypeList(
                            record.productType === ProductType.VEHICLE
                              ? dispathTypeList
                              : vendingTypeList,
                          );
                          form.setFieldValue(
                            'businessType',
                            record.applyVehicleBusinessType,
                          );
                          setIsPublish(res?.data?.isPublish === 1);
                          setAppList(res?.data?.appInfoList || []);
                          res?.data?.appInfoList?.forEach((app: any) => {
                            form.setFieldValue(['appInfoList', app.appName], {
                              label: app.version,
                              value: app.versionNumber,
                            });
                          });
                        }
                      })
                      .catch((err) => {});
                  }}
                >
                  编辑
                </a>
                <a
                  onClick={onDisabled.bind(null, record)}
                  style={{
                    color: record.enable ? '#1677ff' : 'red',
                  }}
                >
                  {record.enable ? '禁用' : '启用'}
                </a>
              </div>
            );
          };
          break;
      }
      return col;
    });
  };

  const fetchTable = (opt: { current: number; pageSize: number }) => {
    const values = formInstance.current.getFieldsValue();
    request({
      path: '/ota/web/product_package_info_get_page_list',
      method: 'POST',
      body: {
        appName: values?.appName?.value,
        appVersionNumber: values?.appVersionNumber?.value,
        productType: values?.productType?.value,
        applyVehicleBusinessType: values?.applyVehicleBusinessType?.value,
        description: values.description,
        pageNum: opt.current,
        pageSize: opt.pageSize
      },
    })
      .then((res: any) => {
        if (res?.code === HttpStatusCode.Success) {
          setTableList(res?.data?.list);
          setPagination({
            total: res?.data?.total,
            totalPage: res?.data?.pages,
          });
        }
      })
      .catch((e) => {});
  };

  const addAndEditProductPackage = async (data: any, type: 'add' | 'edit') => {
    const path =
      type == 'add'
        ? '/ota/web/add_product_package'
        : '/ota/web/edit_product_package_info';
    return request({
      path,
      method: 'POST',
      body: data,
    });
  };

  const getAppVersionInfoList = (productType: string) => {
    return request({
      path: '/ota/web/get_app_latest_version_info_list',
      method: 'POST',
      body: {
        productType,
      },
    });
  };

  const getProductPackageDetailInfo = (productPackageNumber: string) => {
    return request({
      path: '/ota/web/get_product_package_detail_info',
      method: 'POST',
      body: {
        productPackageNumber,
      },
    });
  };

  const closeModal = () => {
    form.resetFields();
    setAppList([]);
    setVisible(false);
    setIsPublish(false);
    setBusinessTypeList(dispathTypeList);
  };

  const renderTipMessage = (record: any) => {
    if (isPublish) {
      return null;
    }
    if (record.versionEnable === 0) {
      return (
        <div className="tip-message" style={{ color: 'red' }}>
          此版本已禁用，请更换
        </div>
      );
    } else if (record.isHaveLatestVersion === 1) {
      return (
        <div className="tip-message" style={{ color: 'red' }}>
          <span>最新版本为：</span>
          <span style={{ whiteSpace: 'normal', wordBreak: 'break-all' }}>
            {record.latestVersion}
          </span>
        </div>
      );
    }
    return null;
  };
  const formatAppColums = (columns: any[]) => {
    return columns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'appName':
          col.render = (text: any, record: any) => {
            const { appEnable, isNew } = record;
            if (isNew === 1) {
              return <>{text}(新增)</>;
            } else if (appEnable === 0) {
              return <>{text}(已禁用)</>;
            }
            return text;
          };
          break;
        case 'version':
          col.render = (text: any, record: any) => {
            const { appEnable, versionInfoList } = record;
            if (appEnable === 0) {
              return record.version;
            }
            const options = record?.versionInfoList
              ?.filter((i: any) => i.enable === 1)
              ?.map((item: any) => ({
                label: item.version,
                value: item.versionNumber,
              }));
            return (
              <Form.Item
                name={['appInfoList', record.appName]}
                help={renderTipMessage(record)}
              >
                <Select
                  placeholder="请选择应用版本"
                  options={options}
                  onChange={(value: any) => {
                    const selectedVersion = record?.versionInfoList
                      ?.filter((i: any) => i.enable === 1)
                      ?.find((i: any) => i.versionNumber === value);
                    record.defect = selectedVersion?.defect || '';
                    record.updateInfo = selectedVersion?.updateInfo || '';
                    setAppList([...appList]);
                  }}
                  disabled={isPublish}
                />
              </Form.Item>
            );
          };
          break;
      }
      return col;
    });
  };

  const addPackage = async () => {
    try {
      const values = await form.validateFields();
      const appInfoList = [];
      for (const [k, v] of Object.entries(values.appInfoList)) {
        const temp = appList.find((appItem) => appItem.appName === k);
        const version = temp?.versionInfoList?.find(
          (i: any) => i.versionNumber === (v?.value || v),
        );
        if (version?.enable === 0) {
          message.error(`${version.version}的版本已禁用，请更换`);
          return;
        }
        appInfoList.push({
          appName: k,
          versionNumber: v?.value || v,
        });
      }
      const params: any = {
        productType: values.productType,
        businessType: values.businessType,
        version: values.version,
        description: values.description,
        appInfoList,
      };
      if (values.productPackageNumber) {
        params.productPackageNumber = values.productPackageNumber;
      }
      const resData: any = await addAndEditProductPackage(params, operateType);
      if (resData.code === HttpStatusCode.Success) {
        message.success('操作成功');
        fetchTable(currentPage);
        closeModal();
      }
    } catch (e: any) {}
  };

  const findFormField = (fieldName: string) => {
    return searchConfig?.fields?.find(
      (field: FieldItem) => field.fieldName === fieldName,
    );
  };

  const getVehicleBusinessType = () => {
    const productType =
      formInstance.current?.getFieldValue('productType')?.value;
    const vehicleBusiness = findFormField('applyVehicleBusinessType');
    vehicleBusiness!.options =
      productType === ProductType.VEHICLE
        ? dispathTypeList
        : productType === ProductType.ROBOT
        ? vendingTypeList
        : [];
    setSearchConfig({
      ...searchConfig,
    });
  };

  const fetchModuleOptions = () => {
    request({
      method: 'POST',
      path: '/ota/web/application_get_list',
      body: {
        enable: 1,
      },
    })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          const appList = findFormField('appName');
          appList!.options = res?.data?.map((i: any) => ({
            label: i.appAlias,
            value: i.appName,
          }));
          setSearchConfig({
            ...searchConfig,
          });
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const onSearchClick = () => {
    fetchTable(currentPage);
  };
  const onResetClick = () => {
    formInstance.current?.resetFields();
    fetchTable(currentPage);
  };
  const getAppVersionList = () => {
    const appName = formInstance.current?.getFieldValue('appName');
    if (!appName?.value) {
      return;
    }
    request({
      method: 'POST',
      path: '/ota/web/application_version_get_list',
      body: {
        appName: appName?.value,
        enable: 1,
      },
    }).then((res: any) => {
      if (res.code === HttpStatusCode.Success) {
        const appVersionNumber = findFormField('appVersionNumber');
        appVersionNumber!.options = res?.data?.map((i: any) => ({
          label: i.version,
          value: i.versionNumber,
        }));
        setSearchConfig({
          ...searchConfig,
        });
      }
    });
  };
  useEffect(() => {
    fetchModuleOptions();
  }, []);
  return (
    <>
      <Button
        style={{
          marginBottom: '10px',
          position: 'absolute',
          top: '-55px',
          right: 0,
        }}
        type="primary"
        onClick={() => {
          setVisible(true);
          setOperateType('add');
        }}
      >
        生成产品包
      </Button>
      <CommonForm
        formConfig={searchConfig}
        layout={'inline'}
        onSearchClick={onSearchClick}
        onResetClick={onResetClick}
        formType="search"
        getFormInstance={(formRef: any) => {
          formInstance.current = formRef;
        }}
        onFieldFocus={(filedName: string, value: any) => {
          filedName === 'appVersionNumber' && getAppVersionList();
          filedName === 'applyVehicleBusinessType' && getVehicleBusinessType();
        }}
        onValueChange={(values: any, changedName: string) => {
          if (changedName === 'appName') {
            formInstance.current?.setFieldValue('appVersionNumber', null);
            const appVersionNumber = findFormField('appVersionNumber');
            appVersionNumber!.options = [];
            setSearchConfig({
              ...searchConfig,
            });
          }
        }}
      />
      <Table
        columns={formatColumns(ProducTableData)}
        dataSource={tableList}
        loading={loading}
        bordered
        scroll={{
          y: 500,
        }}
        pagination={{
          position: ['bottomCenter'],
          total: pagination.total,
          current: currentPage.current,
          pageSize: currentPage.pageSize,
          showQuickJumper: true,
          showSizeChanger: true,
          pageSizeOptions: ['10', '20', '30', '40', '100'],
          showTotal: (total) =>
            `共 ${pagination.totalPage}页,${pagination.total} 条记录`,
        }}
        onChange={(
          paginationData: any,
          filters: any,
          sorter: any,
          extra: any,
        ) => {
          if (extra.action === 'paginate') {
            const { current, pageSize } = paginationData;
            setCurrentPage({
              current,
              pageSize,
            });
          }
        }}
      />
      <Modal
        title="产品包信息"
        open={visible}
        width={800}
        onCancel={closeModal}
        onOk={() => {
          addPackage();
        }}
        destroyOnClose
      >
        <Form
          form={form}
          initialValues={defaultData}
          onFieldsChange={(changedFields, allFields) => {
            console.log(changedFields);
            const changedFieldName = changedFields[0].name[0];
            const formValues = form.getFieldsValue();
            if (changedFieldName == 'productType' && operateType === 'add') {
              const value = formValues[changedFieldName];
              if (value == ProductType.VEHICLE) {
                setBusinessTypeList(dispathTypeList);
              } else if (value == ProductType.ROBOT) {
                setBusinessTypeList(vendingTypeList);
              }
              getAppVersionInfoList(value)
                .then((res: any) => {
                  if (res.code === HttpStatusCode.Success) {
                    setAppList(res.data);
                    res.data?.forEach((i: any) => {
                      form.setFieldValue(['appInfoList', i.appName], {
                        label: i.version,
                        value: i.versionNumber,
                      });
                    });
                  }
                })
                .catch((err) => {});
            }
          }}
        >
          <Form.Item
            name={'productType'}
            label={'所属产品'}
            rules={[{ required: true, message: '请选择所属产品' }]}
          >
            <Radio.Group
              disabled={operateType === 'edit'}
              options={[
                {
                  label: ProductTypeName.VEHICLE,
                  value: ProductType.VEHICLE,
                },
                {
                  label: ProductTypeName.ROBOT,
                  value: ProductType.ROBOT,
                },
              ]}
            />
          </Form.Item>
          <Form.Item
            name={'businessType'}
            label={'所属设备'}
            rules={[{ required: true, message: '请选择所属设备' }]}
          >
            <Radio.Group
              options={businessTypeList}
              disabled={operateType === 'edit'}
            />
          </Form.Item>
          <Form.Item
            name={'version'}
            label={'版本号'}
            rules={[{ required: true, message: '请选择版本号' }]}
          >
            <Input disabled={isPublish} />
          </Form.Item>
          <Form.Item name={'packageType'} label={'选择应用包'}>
            <Table
              rowClassName={(record: any, index: number) =>
                record.appEnable === 0 ? 'row-disabled' : 'row'
              }
              columns={formatAppColums(AppListColumn)}
              dataSource={appList}
              pagination={false}
            ></Table>
          </Form.Item>
          <Form.Item name={'description'} label={'备注信息'}>
            <Input.TextArea maxLength={500} />
          </Form.Item>
          <Form.Item
            hidden
            name={'productPackageNumber'}
            label={'产品包编号'}
          />
        </Form>
      </Modal>
    </>
  );
};

export default AppTable;
