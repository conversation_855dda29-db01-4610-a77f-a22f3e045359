import { request } from '../fetch/core';
import { call, delay, fork, put, take } from 'redux-saga/effects';
import { channel } from 'redux-saga';
import {
  makeFetchSuccessActionType,
  makeFetchFailedActionType,
} from './util';
import AllActionType from './action';
import { HttpStatusCode } from '@/fetch/core/constant';
const startFetch = () => {
  return {
    type: AllActionType.START_FETCH,
    loading: true
  }
};

const fetchSuccess = (actionType: string, data: any) => {
  return {
    type: makeFetchSuccessActionType(actionType),
    payload: data,
    loading: false
  }
};

const fetchFailed = (actionType: string, data: any) => {
  return {
    type: makeFetchFailedActionType(actionType),
    payload: data,
    loading: false
  }
};

function* myRequest(chan: any): any {
  while (true) {
    const payload = yield take(chan);
    const { nextActionFunc, actionType, ...fetchData } = payload;
    try {
      const result = yield call(request, fetchData);
      nextActionFunc && nextActionFunc(result, fetchData);
      yield put(fetchSuccess(actionType, result));
    } catch (err) {
      nextActionFunc && nextActionFunc(err, fetchData);
      yield put(fetchFailed(actionType, err));
    }
  }
}

export function* watchHTTPRequest(): any {
  const chan = yield call(channel);
  // 接口请求串行
  yield fork(myRequest, chan);
  while (true) {
    const data = yield take(AllActionType.SEND_HTTP_REQUEST);
    yield put(chan, data.payload);
  }
}


