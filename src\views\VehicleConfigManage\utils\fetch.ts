import { request } from '@/fetch/core';

export class VehicleConfigManageFetch {
  public fetchConfReferenceCount = (params: { vehicleConfTemplateNumber: string }) => {
    const requestParams: RequestOptions = {
      path: '/ota/web/get_vehicle_conf_template_reference_count',
      body: {
        vehicleConfTemplateNumber: params.vehicleConfTemplateNumber,
      },
      method: 'POST',
    };
    return request(requestParams);
  };
}
