import React, { useState, useEffect, useRef } from 'react';
import { Form, message, Col } from 'antd';
import { useNavigate } from 'react-router-dom';
import BreadCrumb from '@/components/BreadCrumb';
import { formatLocation } from '@/utils/formatLocation';
import FormTitle from '@/components/FormTitle';
import { CustomButton, ButtonType } from '@/components/CustomButton';
import './index.scss';
import { api } from '@/fetch/core/api';
import { request } from '@/fetch/core';
import { HttpStatusCode } from '@/fetch/core/constant';
import SelectConfTable from '@/components/SelectConfTable';
import CheckConf from '@/components/CheckConf';
import { VehicleConfigManageFetch } from '../utils/fetch';
import showModal from '@/components/commonModal';
import { ConfTemplateForm } from '../utils/column';
import { FieldItem, FormConfig } from '@/components/CommonForm/formConfig';
import { cloneDeep } from 'lodash';
import CommonForm from '@/components/CommonForm';
import { ProductType } from '@/utils/constant';
const BreadCrumbItemsMap = new Map([
  [
    'edit',
    [
      { title: 'OTA管理', route: '' },
      { title: '车辆配置模板管理', route: '' },
      { title: '编辑设备配置模板', route: '' },
    ],
  ],
  [
    'add',
    [
      { title: 'OTA管理', route: '' },
      { title: '车辆配置模板管理', route: '' },
      { title: '新建设备配置模板', route: '' },
    ],
  ],
]);
const FormTitleMap = new Map([
  ['edit', '编辑设备配置模板'],
  ['add', '新建设备配置模板'],
]);
const EditAndAddVehicle = () => {
  const fetchApi = new VehicleConfigManageFetch();
  const urlData = formatLocation(window.location.search);
  const type: any = urlData.type;
  const numberRef = useRef<string>(urlData.number);
  const [selectedConfFrom] = Form.useForm();
  const formInstance = useRef<any>(null);
  const navigator = useNavigate();
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const [show, setShow] = useState<boolean>(false);
  const [modalContent, setModalContent] = useState<any>();
  const [modalType, setModalType] = useState<string>();
  const [formData, setFormData] = useState<FormConfig>(
    cloneDeep(ConfTemplateForm),
  );
  const initValues = {
    name: null,
    number: '系统生成',
    vehicleTypeId: null,
    canOverwrite: 0,
  };
  const [selectableConf, setSelectableConf] = useState<any[]>([]);
  const [initSelectedConf, setInitSelectedConf] = useState<any[]>([]);
  const [vehicleTypeOptions, setVehicleTypeOptions] = useState<any[]>([]);
  const [selectableLoading, setSelectableLoading] = useState<boolean>(false);
  const [selectedLoading, setSelectedLoading] = useState<boolean>(false);
  const [checkConfTitle, setCheckConfTitle] = useState<string>('');
  const selectableColumn: any[] = [
    {
      title: '序号',
      dataIndex: 'order',
      align: 'center',
      width: 25,
      render: (text: any, record: any, index: number) => `${index + 1}`,
    },
    {
      title: '模板编号',
      width: 80,
      dataIndex: 'number',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '所在位置',
      width: 50,
      dataIndex: 'position',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '配置文件名称',
      width: 160,
      dataIndex: 'name',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'operate',
      align: 'center',
      width: 30,
      render: (item: any, record: any) => {
        return (
          <div className="operate-btn">
            <a
              onClick={() => {
                fetchSelectableConfContent(record);
                setModalType('check');
                setShow(true);
                setCheckConfTitle('查看配置文件');
              }}
            >
              查看
            </a>
          </div>
        );
      },
    },
  ];
  const selectedColumn: any[] = [
    {
      title: '序号',
      dataIndex: 'order',
      align: 'center',
      width: 40,
      render: (text: any, record: any, index: number) => `${index + 1}`,
    },
    {
      title: '所在位置',
      width: 60,
      dataIndex: 'position',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '配置文件名称',
      width: 170,
      dataIndex: 'name',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '最后操作人',
      width: 100,
      dataIndex: 'modifyUser',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '最后操作时间',
      width: 100,
      dataIndex: 'modifyTime',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'operate',
      align: 'center',
      width: 80,
      render: (item: any, record: any) => {
        return (
          <div className="operate-btn">
            <a
              onClick={() => {
                setModalType('check');
                setShow(true);
                fetchSelectedConfContent(record);
                setCheckConfTitle('查看配置文件');
              }}
            >
              查看
            </a>
            <a onClick={() => delSelected(record)}>删除</a>
          </div>
        );
      },
    },
  ];
  const [canOverwriteMsg, setCanOverwriteMsg] = useState<string>('');
  useEffect(() => {
    if (type === 'edit') {
      fetchConfDetail(urlData.number);
      fetchSelectableConf('', urlData.productType);
      fetchVehicleTypeOptions(urlData.productType);
      const field = formData?.fields?.find(
        (field: FieldItem) => field.fieldName === 'productType',
      );
      field!.disabled = true;
      setFormData({
        ...formData,
      });
    } else {
      formInstance.current.setFieldsValue(initValues);
      fetchSelectableConf('', ProductType.VEHICLE);
      fetchVehicleTypeOptions(ProductType.VEHICLE);
    }
  }, [type, urlData.number]);
  const fetchConfDetail = (number: any) => {
    setSelectedLoading(true);
    try {
      request({
        method: 'POST',
        path: api.getVehicleConfDetail,
        body: {
          number: number,
        },
      }).then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          formInstance.current.setFieldsValue(res.data);
          setCanOverwriteMsg(
            res.data.canOverwrite === 1
              ? '使用该模板的车辆，更改配置字段和值，模板也随之更改'
              : '',
          );
          setInitSelectedConf(res.data.addedConfFileList);
          getDeviceList();
        }
      });
    } catch (e) {
      console.log(e);
    } finally {
      setSelectedLoading(false);
    }
  };
  const fetchSelectableConf = (name: any, productType?: ProductType) => {
    setSelectableLoading(true);
    setSelectedRowKeys([]);
    setSelectedRows([]);
    const opt: any = {
      name: name,
      enable: 1,
    };
    if (productType) {
      opt.productType = productType;
    }
    try {
      request({
        method: 'POST',
        path: api.getConfList,
        body: opt,
      }).then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setSelectableConf(res.data);
        }
      });
    } catch (e) {
      console.log(e);
    } finally {
      setSelectableLoading(false);
    }
  };
  const fetchSelectableConfContent = (record: any) => {
    request({
      method: 'POST',
      path: api.getConfTemplateDetail,
      body: {
        number: record.number,
      },
    })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setModalContent(res.data);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const fetchSelectedConfContent = (record: any) => {
    request({
      method: 'POST',
      path: api.getVehicleConfContent,
      body: {
        id: record.id,
      },
    })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setModalContent(res.data);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const fetchVehicleTypeOptions = (productType: ProductType) => {
    request({
      method: 'POST',
      path: api.getVehicleTypeList,
      body: {
        productType,
      },
    })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setVehicleTypeOptions(res.data);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const fetchSelSelected = (record: any) => {
    request({
      method: 'POST',
      path: api.delVehicleConfFile,
      body: {
        id: record.id,
      },
    })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          onSearchSelectedClick(
            selectedConfFrom.getFieldsValue().inputContainer,
          );
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const delSelected = async (record: any) => {
    // 删除时校验车辆配置模板引用数
    const res: any = await fetchApi.fetchConfReferenceCount({
      vehicleConfTemplateNumber: urlData.number,
    });
    if (res.code === HttpStatusCode.Success && res.data > 0) {
      showModal({
        title: '提示',
        content: (
          <div>
            <div
              style={{ wordBreak: 'break-all' }}
            >{`此文件${record.name}对应的车辆配置模板，已被${res.data}辆车引用，确定删除这些车的该文件吗？`}</div>
            <div style={{ color: '#808080', marginTop: '5px' }}>
              注：如更改少量车，请在【车辆配置与发布】→操作“车辆配置”→操作“另存模版”，并将“新模板”应用到少量车。
            </div>
          </div>
        ),
        type: 'confirm',
        onOk: (cb: any) => {
          fetchSelSelected(record);
          cb();
        },
        onCancel: () => {},
      });
    } else if (res.code === HttpStatusCode.Success && res.data <= 0) {
      showModal({
        title: '提示',
        content: (
          <p
            style={{ wordBreak: 'break-all' }}
          >{`确定删除该文件${record.name}吗？`}</p>
        ),
        type: 'confirm',
        onOk: (cb: any) => {
          fetchSelSelected(record);
          cb();
        },
        onCancel: () => {},
      });
    } else {
      message.error(res.message);
    }
  };
  const onAdd = async () => {
    try {
      // 校验是否选择了文件
      if (selectedRows?.length <= 0) {
        message.error('请先选择车辆配置再添加');
        return;
      }
      // 校验重复文件
      const selectedRowsMap = new Map();
      selectedRows?.forEach((element: any) => {
        selectedRowsMap.set(element.number, {
          name: element.name,
          position: element.position,
        });
      });
      let repeatList: any[] = [];
      initSelectedConf?.forEach((value: any) => {
        if (
          selectedRowsMap.get(value.confTemplateNumber) &&
          selectedRowsMap.get(value.confTemplateNumber).position ===
            value.position &&
          selectedRowsMap.get(value.confTemplateNumber).name === value.name
        ) {
          repeatList.push(value.name);
        }
      });
      if (repeatList.length > 0) {
        message.error({
          content: `${repeatList.toString()}已存在，不允许重复添加！`,
          style: {
            wordBreak: 'break-all',
            wordWrap: 'break-word',
          },
        });
        return;
      }
      if (type === 'add') {
        addContent(selectedRows, selectedRowKeys);
        return;
      }
      // 编辑时校验车辆配置模板引用数
      const res: any = await fetchApi.fetchConfReferenceCount({
        vehicleConfTemplateNumber: urlData.number,
      });
      if (res.code === HttpStatusCode.Success && res.data > 0) {
        showModal({
          title: '提示',
          content: (
            <div>
              <div>{`此模板已被${res.data}辆车引用，确定更新这些车的车辆配置模版吗？`}</div>
              <div style={{ color: '#808080', marginTop: '5px' }}>
                注：如更改少量车，请在【车辆配置与发布】→操作“车辆配置”→操作“另存模版”，并将“新模板”应用到少量车。
              </div>
            </div>
          ),
          type: 'confirm',
          onOk: (cb: any) => {
            addContent(selectedRows, selectedRowKeys);
            cb();
          },
          onCancel: () => {},
        });
      } else if (res.code === HttpStatusCode.Success && res.data <= 0) {
        addContent(selectedRows, selectedRowKeys);
      } else {
        message.error(res.message);
      }
    } catch (e: any) {
      if (e.message) {
        message.error(e.message);
      }
    }
  };
  const addContent = async (selectedRows: any, selectedRowKeys: any) => {
    const data = await formInstance.current.validateFields();
    request({
      method: 'POST',
      path: api.addVehicleConf,
      body: {
        number: data.number === '系统生成' ? null : data.number,
        name: data.name,
        productType: data.productType,
        canOverwrite: data.canOverwrite,
        vehicleTypeId: data.vehicleTypeId?.value || data.vehicleTypeId,
        confTemplateNumberList: selectedRowKeys,
      },
    })
      .then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          numberRef.current = res.data.number;
          selectedConfFrom.getFieldsValue().inputContainer
            ? onSearchSelectedClick(
                selectedConfFrom.getFieldsValue().inputContainer,
              )
            : fetchConfDetail(res.data.number);
          setSelectedRowKeys([]);
          setSelectedRows([]);
          message.success('信息确认添加并提交成功！');
        } else {
          res.message && message.error(res.message);
        }
      })
      .catch((e) => {
        message.error(`必填项未填写全，操作失败！`);
      });
  };
  const onSearchSelectedClick = (inputName: any) => {
    setSelectedLoading(true);
    setSelectedRowKeys([]);
    setSelectedRows([]);
    try {
      request({
        method: 'POST',
        path: api.getAddedVehicleConf,
        body: {
          name: inputName,
          vehicleConfTemplateNumber: urlData.number || numberRef.current,
        },
      }).then((res: any) => {
        if (res && res.code === HttpStatusCode.Success) {
          setInitSelectedConf(res.data);
        }
      });
    } catch (e) {
      console.log(e);
    } finally {
      setSelectedLoading(false);
    }
  };

  const getDeviceList = () => {
    const field = formData?.fields?.find(
      (field: FieldItem) => field.fieldName === 'vehicleTypeId',
    );
    const value = formInstance.current?.getFieldValue('productType');
    request({
      path: '/ota/web/get_vehicle_type_list',
      method: 'POST',
      body: {
        productType: value,
      },
    })
      .then((res: any) => {
        if (res?.code === HttpStatusCode.Success) {
          const options = res?.data?.map((item: any) => ({
            label: item.vehicleTypeName,
            value: item.vehicleTypeId,
          }));
          field!.options = options;
        }
        setFormData({
          ...formData,
        });
      })
      .catch((e: any) => {});
  };

  return (
    <div className="vehicle-edit-content">
      <BreadCrumb items={BreadCrumbItemsMap.get(type)} />
      <div className="vehicle-config-edit">
        <FormTitle title={FormTitleMap.get(type)} />
        <div className="edit-container">
          <CommonForm
            formConfig={formData}
            layout="vertical"
            getFormInstance={(formRef: any) => {
              formInstance.current = formRef;
              if (urlData.type === 'add') {
                formInstance.current.setFieldsValue({
                  productType: ProductType.VEHICLE,
                });
                getDeviceList();
              }
            }}
            onValueChange={(formValues: any, changedFieldName: string) => {
              if (changedFieldName === 'productType') {
                formInstance.current.setFieldValue('vehicleTypeId', null);
                const value = formValues.productType;
                fetchSelectableConf('', value);
                getDeviceList();
              }
            }}
          />
          <Col span={23} push={1}>
            <SelectConfTable
              title={'可选车辆配置'}
              onSearchClick={(inputName: any) => {
                const value = formInstance.current.getFieldsValue().productType;
                fetchSelectableConf(inputName, value);
              }}
              dataSource={selectableConf}
              loading={selectableLoading}
              tableColumns={selectableColumn}
              isSelect={true}
              selectedRowKeys={selectedRowKeys}
              selectedRows={selectedRows}
              saveSelectedConf={(selectedRowKeys: any, selectedRows: any) => {
                setSelectedRowKeys(selectedRowKeys);
                setSelectedRows(selectedRows);
              }}
              columnWidth={'16px'}
              rowKey={'number'}
            />
            <div
              className="submit-btn"
              style={{ marginTop: '50px', marginBottom: '50px' }}
            >
              <CustomButton
                buttonType={ButtonType.DefaultButton}
                onSubmitClick={onAdd}
                height={50}
                otherCSSProperties={{ width: '300px' }}
                title={'确认添加并提交'}
              />
            </div>
            <span
              style={{
                marginLeft: '100px',
                color: 'rgba(0, 0, 0, 0.45)',
              }}
            >
              说明：如所选配置文件在车型配置中已存在，则从车型配置中获取该文件到“车辆配置模板”中。
            </span>
            <SelectConfTable
              title={'已添加车辆配置'}
              onSearchClick={(inputName: any) =>
                onSearchSelectedClick(inputName)
              }
              dataSource={initSelectedConf}
              loading={selectedLoading}
              tableColumns={selectedColumn}
              isSelect={false}
              rowKey={'confTemplateNumber'}
              formRef={selectedConfFrom}
            />
          </Col>
        </div>
        <div className="submit-btn">
          <CustomButton
            buttonType={ButtonType.DefaultButton}
            onSubmitClick={() => navigator('/vehicleConfigManage')}
            title={'返回'}
          />
        </div>
      </div>
      {show && (
        <CheckConf
          show={show}
          onCancel={() => {
            setShow(false);
          }}
          content={modalContent}
          type={modalType}
          title={checkConfTitle}
        />
      )}
    </div>
  );
};
export default React.memo(EditAndAddVehicle);
