import React, { useEffect, useState, useMemo } from 'react';
import { CommonTable } from '@jd/x-coreui';
import { formatLocation } from '@/utils/formatLocation';
import { ConfigChangeTableColumns } from '../utils/colunms';
import getDeviceConfigChangeRecordList from '@/fetch/bussiness/deviceConfigManage';
import { message } from 'antd';
import { HttpStatusCode } from '@/fetch/core/constant';
import { useNavigate } from 'react-router-dom';
import './index.scss';

const deviceConfigManageApi = new getDeviceConfigChangeRecordList();

function DeviceConfigChange() {
  const { deviceName } = formatLocation(window.location.search);
  const navigator = useNavigate();
  const [tableData, setTableData] = useState<any>({});

  useEffect(() => {
    if (!deviceName) {
      message.error('设备名称不能为空');
      return;
    }
    try {
      deviceConfigManageApi
        .getDeviceConfigChangeRecordList({
          deviceName,
        })
        .then((res) => {
          if (res.code === HttpStatusCode.Success) {
            setTableData({
              list: res?.data,
            });
          } else {
            message.error(res?.message || '获取设备配置变更记录失败');
          }
        });
    } catch (err) {
      message.error('获取设备配置变更记录失败');
    }
  }, []);

  const formatColumns = useMemo(() => {
    const operateBtnsList = [
      {
        title: '发版',
        onClick: (record: any) => {
          navigator(`/`);
        },
      },
      {
        title: '编辑',
        onClick: (record: any) => {
          navigator(`/`);
        },
      },
      {
        title: '查看配置',
        onClick: (record: any) => {
          navigator(`/`);
        },
      },
      {
        title: '版本对比',
        onClick: (record: any) => {
          navigator(`/`);
        },
      },
    ];
    return ConfigChangeTableColumns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${index + 1}`;
          break;
        case 'operateVersion':
          col.render = (text: any, record: any, index: number) => {
            if (index === 0) {
              return (
                <>
                  <span className="green-circle"></span>
                  <span>{text || '-'}</span>
                </>
              );
            }
            return `${text || '-'}`;
          };
          break;
        case 'operate':
          col.render = (item: any, record: any) => {
            return (
              <div className="operate-btn">
                {operateBtnsList.map((btn: any, index: number) => (
                  <a
                    key={`operate-${index}`}
                    onClick={() => {
                      btn.onClick(record);
                    }}
                  >
                    {btn.title}
                  </a>
                ))}
              </div>
            );
          };
          break;
        default:
          col.render = (text: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  }, []);

  return (
    <div className="device-config-change">
      <div className="title">{deviceName}配置变更记录</div>
      <CommonTable tableListData={tableData} columns={formatColumns} notPage />
    </div>
  );
}

export default DeviceConfigChange;
