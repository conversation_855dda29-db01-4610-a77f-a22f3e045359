// 线上
import { Cls<PERSON><PERSON><PERSON> } from '@/utils/searchFormEnum';
export const BuryPointClstagMap = new Map([
  // 车辆配置与发布查询条件
  [ClstagKey.release_station, 'release_1689148901877|1'],
  [ClstagKey.release_vehiclename, 'release_1689148901877|2'],
  [ClstagKey.release_ownerUseCase, 'release_1689148901877|3'],
  [ClstagKey.release_vehicleConfTemplate, 'release_1689148901877|4'],
  [ClstagKey.release_vehicleConfIssueStatus, 'release_1689148901877|5'],
  [ClstagKey.release_latestIssueTaskResult, 'release_1689148901877|6'],
  [ClstagKey.release_pushStreamDevice, 'release_1689148901877|7'],
  [ClstagKey.release_videoCamera, 'release_1689148901877|8'],
  [ClstagKey.release_version, 'release_1689148901877|9'],
  [ClstagKey.release_vehicleType, 'release_1689148901877|10'],
  [ClstagKey.release_vehicleClassification, 'release_1689148901877|17'],
  [ClstagKey.release_appName, 'release_1689148901877|18'],
  [ClstagKey.release_city, 'release_1689148901877|19'],
  [ClstagKey.release_province, 'release_1689148901877|20'],

  // 车辆配置与发布操作按钮
  [ClstagKey.release_batchChangeConfig, 'release_1689148901877|11'],
  [ClstagKey.release_configAndRelease, 'release_1689148901877|12'],
  [ClstagKey.release_vehicleConfig, 'release_1689148901877|13'],
  [ClstagKey.release_configOperationRecord, 'release_1689148901877|14'],
  [ClstagKey.release_releaseRecord, 'release_1689148901877|15'],
  [ClstagKey.release_viewReleasePlan, 'release_1689148901877|16'],
]);
